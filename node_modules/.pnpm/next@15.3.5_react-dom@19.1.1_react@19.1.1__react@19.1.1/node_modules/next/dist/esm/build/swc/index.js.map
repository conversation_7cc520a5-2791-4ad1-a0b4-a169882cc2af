{"version": 3, "sources": ["../../../src/build/swc/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-use-before-define */\nimport path from 'path'\nimport { pathToFileURL } from 'url'\nimport { arch, platform } from 'os'\nimport { platformArchTriples } from 'next/dist/compiled/@napi-rs/triples'\nimport * as Log from '../output/log'\nimport { getParserOptions } from './options'\nimport { eventSwcLoadFailure } from '../../telemetry/events/swc-load-failure'\nimport { patchIncorrectLockfile } from '../../lib/patch-incorrect-lockfile'\nimport { downloadNativeNextSwc, downloadWasmSwc } from '../../lib/download-swc'\nimport type {\n  NextConfigComplete,\n  TurbopackLoaderItem,\n  TurbopackRuleConfigItem,\n  TurbopackRuleConfigItemOptions,\n  TurbopackRuleConfigItemOrShortcut,\n} from '../../server/config-shared'\nimport { isDeepStrictEqual } from 'util'\nimport {\n  type DefineEnvPluginOptions,\n  getDefineEnv,\n} from '../webpack/plugins/define-env-plugin'\nimport { getReactCompilerLoader } from '../get-babel-loader-config'\nimport type {\n  NapiPartialProjectOptions,\n  NapiProjectOptions,\n} from './generated-native'\nimport type {\n  Binding,\n  DefineEnv,\n  Endpoint,\n  HmrIdentifiers,\n  Project,\n  ProjectOptions,\n  RawEntrypoints,\n  Route,\n  TurboEngineOptions,\n  TurbopackResult,\n  TurbopackStackFrame,\n  Update,\n  UpdateMessage,\n  WrittenEndpoint,\n} from './types'\nimport { TurbopackInternalError } from '../../shared/lib/turbopack/utils'\n\ntype RawBindings = typeof import('./generated-native')\ntype RawWasmBindings = typeof import('./generated-wasm') & {\n  default?(): Promise<typeof import('./generated-wasm')>\n}\n\nconst nextVersion = process.env.__NEXT_VERSION as string\n\nconst ArchName = arch()\nconst PlatformName = platform()\n\nfunction infoLog(...args: any[]) {\n  if (process.env.NEXT_PRIVATE_BUILD_WORKER) {\n    return\n  }\n  if (process.env.DEBUG) {\n    Log.info(...args)\n  }\n}\n\n/**\n * Based on napi-rs's target triples, returns triples that have corresponding next-swc binaries.\n */\nexport function getSupportedArchTriples(): Record<string, any> {\n  const { darwin, win32, linux, freebsd, android } = platformArchTriples\n\n  return {\n    darwin,\n    win32: {\n      arm64: win32.arm64,\n      ia32: win32.ia32.filter((triple) => triple.abi === 'msvc'),\n      x64: win32.x64.filter((triple) => triple.abi === 'msvc'),\n    },\n    linux: {\n      // linux[x64] includes `gnux32` abi, with x64 arch.\n      x64: linux.x64.filter((triple) => triple.abi !== 'gnux32'),\n      arm64: linux.arm64,\n      // This target is being deprecated, however we keep it in `knownDefaultWasmFallbackTriples` for now\n      arm: linux.arm,\n    },\n    // Below targets are being deprecated, however we keep it in `knownDefaultWasmFallbackTriples` for now\n    freebsd: {\n      x64: freebsd.x64,\n    },\n    android: {\n      arm64: android.arm64,\n      arm: android.arm,\n    },\n  }\n}\n\nconst triples = (() => {\n  const supportedArchTriples = getSupportedArchTriples()\n  const targetTriple = supportedArchTriples[PlatformName]?.[ArchName]\n\n  // If we have supported triple, return it right away\n  if (targetTriple) {\n    return targetTriple\n  }\n\n  // If there isn't corresponding target triple in `supportedArchTriples`, check if it's excluded from original raw triples\n  // Otherwise, it is completely unsupported platforms.\n  let rawTargetTriple = platformArchTriples[PlatformName]?.[ArchName]\n\n  if (rawTargetTriple) {\n    Log.warn(\n      `Trying to load next-swc for target triple ${rawTargetTriple}, but there next-swc does not have native bindings support`\n    )\n  } else {\n    Log.warn(\n      `Trying to load next-swc for unsupported platforms ${PlatformName}/${ArchName}`\n    )\n  }\n\n  return []\n})()\n\n// Allow to specify an absolute path to the custom turbopack binary to load.\n// If one of env variables is set, `loadNative` will try to use specified\n// binary instead. This is thin, naive interface\n// - `loadBindings` will not validate neither path nor the binary.\n//\n// Note these are internal flag: there's no stability, feature guarantee.\nconst __INTERNAL_CUSTOM_TURBOPACK_BINDINGS =\n  process.env.__INTERNAL_CUSTOM_TURBOPACK_BINDINGS\n\nfunction checkVersionMismatch(pkgData: any) {\n  const version = pkgData.version\n\n  if (version && version !== nextVersion) {\n    Log.warn(\n      `Mismatching @next/swc version, detected: ${version} while Next.js is on ${nextVersion}. Please ensure these match`\n    )\n  }\n}\n\n// These are the platforms we'll try to load wasm bindings first,\n// only try to load native bindings if loading wasm binding somehow fails.\n// Fallback to native binding is for migration period only,\n// once we can verify loading-wasm-first won't cause visible regressions,\n// we'll not include native bindings for these platform at all.\nconst knownDefaultWasmFallbackTriples = [\n  'x86_64-unknown-freebsd',\n  'aarch64-linux-android',\n  'arm-linux-androideabi',\n  'armv7-unknown-linux-gnueabihf',\n  'i686-pc-windows-msvc',\n  // WOA targets are TBD, while current userbase is small we may support it in the future\n  //'aarch64-pc-windows-msvc',\n]\n\n// The last attempt's error code returned when cjs require to native bindings fails.\n// If node.js throws an error without error code, this should be `unknown` instead of undefined.\n// For the wasm-first targets (`knownDefaultWasmFallbackTriples`) this will be `unsupported_target`.\nlet lastNativeBindingsLoadErrorCode:\n  | 'unknown'\n  | 'unsupported_target'\n  | string\n  | undefined = undefined\nlet nativeBindings: Binding\nlet wasmBindings: Binding\nlet downloadWasmPromise: any\nlet pendingBindings: any\nlet swcTraceFlushGuard: any\nlet downloadNativeBindingsPromise: Promise<void> | undefined = undefined\n\nexport const lockfilePatchPromise: { cur?: Promise<void> } = {}\n\nexport async function loadBindings(\n  useWasmBinary: boolean = false\n): Promise<Binding> {\n  // Increase Rust stack size as some npm packages being compiled need more than the default.\n  if (!process.env.RUST_MIN_STACK) {\n    process.env.RUST_MIN_STACK = '8388608'\n  }\n\n  if (pendingBindings) {\n    return pendingBindings\n  }\n\n  // rust needs stdout to be blocking, otherwise it will throw an error (on macOS at least) when writing a lot of data (logs) to it\n  // see https://github.com/napi-rs/napi-rs/issues/1630\n  // and https://github.com/nodejs/node/blob/main/doc/api/process.md#a-note-on-process-io\n  if (process.stdout._handle != null) {\n    // @ts-ignore\n    process.stdout._handle.setBlocking?.(true)\n  }\n  if (process.stderr._handle != null) {\n    // @ts-ignore\n    process.stderr._handle.setBlocking?.(true)\n  }\n\n  pendingBindings = new Promise(async (resolve, _reject) => {\n    if (!lockfilePatchPromise.cur) {\n      // always run lockfile check once so that it gets patched\n      // even if it doesn't fail to load locally\n      lockfilePatchPromise.cur = patchIncorrectLockfile(process.cwd()).catch(\n        console.error\n      )\n    }\n\n    let attempts: any[] = []\n    const disableWasmFallback = process.env.NEXT_DISABLE_SWC_WASM\n    const unsupportedPlatform = triples.some(\n      (triple: any) =>\n        !!triple?.raw && knownDefaultWasmFallbackTriples.includes(triple.raw)\n    )\n    const isWebContainer = process.versions.webcontainer\n    // Normal execution relies on the param `useWasmBinary` flag to load, but\n    // in certain cases where there isn't a native binary we always load wasm fallback first.\n    const shouldLoadWasmFallbackFirst =\n      (!disableWasmFallback && useWasmBinary) ||\n      unsupportedPlatform ||\n      isWebContainer\n\n    if (!unsupportedPlatform && useWasmBinary) {\n      Log.warn(\n        `experimental.useWasmBinary is not an option for supported platform ${PlatformName}/${ArchName} and will be ignored.`\n      )\n    }\n\n    if (shouldLoadWasmFallbackFirst) {\n      lastNativeBindingsLoadErrorCode = 'unsupported_target'\n      const fallbackBindings = await tryLoadWasmWithFallback(attempts)\n      if (fallbackBindings) {\n        return resolve(fallbackBindings)\n      }\n    }\n\n    // Trickle down loading `fallback` bindings:\n    //\n    // - First, try to load native bindings installed in node_modules.\n    // - If that fails with `ERR_MODULE_NOT_FOUND`, treat it as case of https://github.com/npm/cli/issues/4828\n    // that host system where generated package lock is not matching to the guest system running on, try to manually\n    // download corresponding target triple and load it. This won't be triggered if native bindings are failed to load\n    // with other reasons than `ERR_MODULE_NOT_FOUND`.\n    // - Lastly, falls back to wasm binding where possible.\n    try {\n      return resolve(loadNative())\n    } catch (a) {\n      if (\n        Array.isArray(a) &&\n        a.every((m) => m.includes('it was not installed'))\n      ) {\n        let fallbackBindings = await tryLoadNativeWithFallback(attempts)\n\n        if (fallbackBindings) {\n          return resolve(fallbackBindings)\n        }\n      }\n\n      attempts = attempts.concat(a)\n    }\n\n    // For these platforms we already tried to load wasm and failed, skip reattempt\n    if (!shouldLoadWasmFallbackFirst && !disableWasmFallback) {\n      const fallbackBindings = await tryLoadWasmWithFallback(attempts)\n      if (fallbackBindings) {\n        return resolve(fallbackBindings)\n      }\n    }\n\n    logLoadFailure(attempts, true)\n  })\n  return pendingBindings\n}\n\nasync function tryLoadNativeWithFallback(attempts: Array<string>) {\n  const nativeBindingsDirectory = path.join(\n    path.dirname(require.resolve('next/package.json')),\n    'next-swc-fallback'\n  )\n\n  if (!downloadNativeBindingsPromise) {\n    downloadNativeBindingsPromise = downloadNativeNextSwc(\n      nextVersion,\n      nativeBindingsDirectory,\n      triples.map((triple: any) => triple.platformArchABI)\n    )\n  }\n  await downloadNativeBindingsPromise\n\n  try {\n    return loadNative(nativeBindingsDirectory)\n  } catch (a: any) {\n    attempts.push(...[].concat(a))\n  }\n\n  return undefined\n}\n\nasync function tryLoadWasmWithFallback(attempts: any[]) {\n  try {\n    let bindings = await loadWasm('')\n    // @ts-expect-error TODO: this event has a wrong type.\n    eventSwcLoadFailure({\n      wasm: 'enabled',\n      nativeBindingsErrorCode: lastNativeBindingsLoadErrorCode,\n    })\n    return bindings\n  } catch (a: any) {\n    attempts.push(...[].concat(a))\n  }\n\n  try {\n    // if not installed already download wasm package on-demand\n    // we download to a custom directory instead of to node_modules\n    // as node_module import attempts are cached and can't be re-attempted\n    // x-ref: https://github.com/nodejs/modules/issues/307\n    const wasmDirectory = path.join(\n      path.dirname(require.resolve('next/package.json')),\n      'wasm'\n    )\n    if (!downloadWasmPromise) {\n      downloadWasmPromise = downloadWasmSwc(nextVersion, wasmDirectory)\n    }\n    await downloadWasmPromise\n    let bindings = await loadWasm(wasmDirectory)\n    // @ts-expect-error TODO: this event has a wrong type.\n    eventSwcLoadFailure({\n      wasm: 'fallback',\n      nativeBindingsErrorCode: lastNativeBindingsLoadErrorCode,\n    })\n\n    // still log native load attempts so user is\n    // aware it failed and should be fixed\n    for (const attempt of attempts) {\n      Log.warn(attempt)\n    }\n    return bindings\n  } catch (a: any) {\n    attempts.push(...[].concat(a))\n  }\n}\n\nfunction loadBindingsSync() {\n  let attempts: any[] = []\n  try {\n    return loadNative()\n  } catch (a) {\n    attempts = attempts.concat(a)\n  }\n\n  // we can leverage the wasm bindings if they are already\n  // loaded\n  if (wasmBindings) {\n    return wasmBindings\n  }\n\n  logLoadFailure(attempts)\n  throw new Error('Failed to load bindings', { cause: attempts })\n}\n\nlet loggingLoadFailure = false\n\nfunction logLoadFailure(attempts: any, triedWasm = false) {\n  // make sure we only emit the event and log the failure once\n  if (loggingLoadFailure) return\n  loggingLoadFailure = true\n\n  for (let attempt of attempts) {\n    Log.warn(attempt)\n  }\n\n  // @ts-expect-error TODO: this event has a wrong type.\n  eventSwcLoadFailure({\n    wasm: triedWasm ? 'failed' : undefined,\n    nativeBindingsErrorCode: lastNativeBindingsLoadErrorCode,\n  })\n    .then(() => lockfilePatchPromise.cur || Promise.resolve())\n    .finally(() => {\n      Log.error(\n        `Failed to load SWC binary for ${PlatformName}/${ArchName}, see more info here: https://nextjs.org/docs/messages/failed-loading-swc`\n      )\n      process.exit(1)\n    })\n}\n\ntype RustifiedEnv = { name: string; value: string }[]\n\nexport function createDefineEnv({\n  isTurbopack,\n  clientRouterFilters,\n  config,\n  dev,\n  distDir,\n  fetchCacheKeyPrefix,\n  hasRewrites,\n  middlewareMatchers,\n}: Omit<\n  DefineEnvPluginOptions,\n  'isClient' | 'isNodeOrEdgeCompilation' | 'isEdgeServer' | 'isNodeServer'\n>): DefineEnv {\n  let defineEnv: DefineEnv = {\n    client: [],\n    edge: [],\n    nodejs: [],\n  }\n\n  for (const variant of Object.keys(defineEnv) as (keyof typeof defineEnv)[]) {\n    defineEnv[variant] = rustifyEnv(\n      getDefineEnv({\n        isTurbopack,\n        clientRouterFilters,\n        config,\n        dev,\n        distDir,\n        fetchCacheKeyPrefix,\n        hasRewrites,\n        isClient: variant === 'client',\n        isEdgeServer: variant === 'edge',\n        isNodeOrEdgeCompilation: variant === 'nodejs' || variant === 'edge',\n        isNodeServer: variant === 'nodejs',\n        middlewareMatchers,\n      })\n    )\n  }\n\n  return defineEnv\n}\n\nfunction rustifyEnv(env: Record<string, string>): RustifiedEnv {\n  return Object.entries(env)\n    .filter(([_, value]) => value != null)\n    .map(([name, value]) => ({\n      name,\n      value,\n    }))\n}\n\n// TODO(sokra) Support wasm option.\nfunction bindingToApi(\n  binding: RawBindings,\n  _wasm: boolean\n): Binding['turbo']['createProject'] {\n  type NativeFunction<T> = (\n    callback: (err: Error, value: T) => void\n  ) => Promise<{ __napiType: 'RootTask' }>\n\n  type NapiEndpoint = { __napiType: 'Endpoint' }\n\n  type NapiEntrypoints = {\n    routes: NapiRoute[]\n    middleware?: NapiMiddleware\n    instrumentation?: NapiInstrumentation\n    pagesDocumentEndpoint: NapiEndpoint\n    pagesAppEndpoint: NapiEndpoint\n    pagesErrorEndpoint: NapiEndpoint\n  }\n\n  type NapiMiddleware = {\n    endpoint: NapiEndpoint\n    runtime: 'nodejs' | 'edge'\n    matcher?: string[]\n  }\n\n  type NapiInstrumentation = {\n    nodeJs: NapiEndpoint\n    edge: NapiEndpoint\n  }\n\n  type NapiRoute = {\n    pathname: string\n  } & (\n    | {\n        type: 'page'\n        htmlEndpoint: NapiEndpoint\n        dataEndpoint: NapiEndpoint\n      }\n    | {\n        type: 'page-api'\n        endpoint: NapiEndpoint\n      }\n    | {\n        type: 'app-page'\n        pages: {\n          originalName: string\n          htmlEndpoint: NapiEndpoint\n          rscEndpoint: NapiEndpoint\n        }[]\n      }\n    | {\n        type: 'app-route'\n        originalName: string\n        endpoint: NapiEndpoint\n      }\n    | {\n        type: 'conflict'\n      }\n  )\n\n  const cancel = new (class Cancel extends Error {})()\n\n  /**\n   * Utility function to ensure all variants of an enum are handled.\n   */\n  function invariant(\n    never: never,\n    computeMessage: (arg: any) => string\n  ): never {\n    throw new Error(`Invariant: ${computeMessage(never)}`)\n  }\n\n  async function withErrorCause<T>(fn: () => Promise<T>): Promise<T> {\n    try {\n      return await fn()\n    } catch (nativeError: any) {\n      throw TurbopackInternalError.createAndRecordTelemetry(nativeError)\n    }\n  }\n\n  /**\n   * Calls a native function and streams the result.\n   * If useBuffer is true, all values will be preserved, potentially buffered\n   * if consumed slower than produced. Else, only the latest value will be\n   * preserved.\n   */\n  function subscribe<T>(\n    useBuffer: boolean,\n    nativeFunction:\n      | NativeFunction<T>\n      | ((callback: (err: Error, value: T) => void) => Promise<void>)\n  ): AsyncIterableIterator<T> {\n    type BufferItem =\n      | { err: Error; value: undefined }\n      | { err: undefined; value: T }\n    // A buffer of produced items. This will only contain values if the\n    // consumer is slower than the producer.\n    let buffer: BufferItem[] = []\n    // A deferred value waiting for the next produced item. This will only\n    // exist if the consumer is faster than the producer.\n    let waiting:\n      | {\n          resolve: (value: T) => void\n          reject: (error: Error) => void\n        }\n      | undefined\n    let canceled = false\n\n    // The native function will call this every time it emits a new result. We\n    // either need to notify a waiting consumer, or buffer the new result until\n    // the consumer catches up.\n    function emitResult(err: Error | undefined, value: T | undefined) {\n      if (waiting) {\n        let { resolve, reject } = waiting\n        waiting = undefined\n        if (err) reject(err)\n        else resolve(value!)\n      } else {\n        const item = { err, value } as BufferItem\n        if (useBuffer) buffer.push(item)\n        else buffer[0] = item\n      }\n    }\n\n    async function* createIterator() {\n      const task = await withErrorCause<{ __napiType: 'RootTask' } | void>(() =>\n        nativeFunction(emitResult)\n      )\n      try {\n        while (!canceled) {\n          if (buffer.length > 0) {\n            const item = buffer.shift()!\n            if (item.err) throw item.err\n            yield item.value\n          } else {\n            // eslint-disable-next-line no-loop-func\n            yield new Promise<T>((resolve, reject) => {\n              waiting = { resolve, reject }\n            })\n          }\n        }\n      } catch (e) {\n        if (e === cancel) return\n        if (e instanceof Error) {\n          throw TurbopackInternalError.createAndRecordTelemetry(e)\n        }\n        throw e\n      } finally {\n        if (task) {\n          binding.rootTaskDispose(task)\n        }\n      }\n    }\n\n    const iterator = createIterator()\n    iterator.return = async () => {\n      canceled = true\n      if (waiting) waiting.reject(cancel)\n      return { value: undefined, done: true } as IteratorReturnResult<never>\n    }\n    return iterator\n  }\n\n  async function rustifyProjectOptions(\n    options: ProjectOptions\n  ): Promise<NapiProjectOptions> {\n    return {\n      ...options,\n      nextConfig: await serializeNextConfig(\n        options.nextConfig,\n        options.projectPath!\n      ),\n      jsConfig: JSON.stringify(options.jsConfig),\n      env: rustifyEnv(options.env),\n    }\n  }\n\n  async function rustifyPartialProjectOptions(\n    options: Partial<ProjectOptions>\n  ): Promise<NapiPartialProjectOptions> {\n    return {\n      ...options,\n      nextConfig:\n        options.nextConfig &&\n        (await serializeNextConfig(options.nextConfig, options.projectPath!)),\n      jsConfig: options.jsConfig && JSON.stringify(options.jsConfig),\n      env: options.env && rustifyEnv(options.env),\n    }\n  }\n\n  class ProjectImpl implements Project {\n    private readonly _nativeProject: { __napiType: 'Project' }\n\n    constructor(nativeProject: { __napiType: 'Project' }) {\n      this._nativeProject = nativeProject\n    }\n\n    async update(options: Partial<ProjectOptions>) {\n      await withErrorCause(async () =>\n        binding.projectUpdate(\n          this._nativeProject,\n          await rustifyPartialProjectOptions(options)\n        )\n      )\n    }\n\n    async writeAllEntrypointsToDisk(\n      appDirOnly: boolean\n    ): Promise<TurbopackResult<RawEntrypoints>> {\n      return await withErrorCause(async () => {\n        const napiEndpoints = (await binding.projectWriteAllEntrypointsToDisk(\n          this._nativeProject,\n          appDirOnly\n        )) as TurbopackResult<NapiEntrypoints>\n\n        return napiEntrypointsToRawEntrypoints(napiEndpoints)\n      })\n    }\n\n    entrypointsSubscribe() {\n      const subscription = subscribe<TurbopackResult<NapiEntrypoints>>(\n        false,\n        async (callback) =>\n          binding.projectEntrypointsSubscribe(this._nativeProject, callback)\n      )\n      return (async function* () {\n        for await (const entrypoints of subscription) {\n          yield napiEntrypointsToRawEntrypoints(entrypoints)\n        }\n      })()\n    }\n\n    hmrEvents(identifier: string) {\n      return subscribe<TurbopackResult<Update>>(true, async (callback) =>\n        binding.projectHmrEvents(this._nativeProject, identifier, callback)\n      )\n    }\n\n    hmrIdentifiersSubscribe() {\n      return subscribe<TurbopackResult<HmrIdentifiers>>(\n        false,\n        async (callback) =>\n          binding.projectHmrIdentifiersSubscribe(this._nativeProject, callback)\n      )\n    }\n\n    traceSource(\n      stackFrame: TurbopackStackFrame,\n      currentDirectoryFileUrl: string\n    ): Promise<TurbopackStackFrame | null> {\n      return binding.projectTraceSource(\n        this._nativeProject,\n        stackFrame,\n        currentDirectoryFileUrl\n      )\n    }\n\n    getSourceForAsset(filePath: string): Promise<string | null> {\n      return binding.projectGetSourceForAsset(this._nativeProject, filePath)\n    }\n\n    getSourceMap(filePath: string): Promise<string | null> {\n      return binding.projectGetSourceMap(this._nativeProject, filePath)\n    }\n\n    getSourceMapSync(filePath: string): string | null {\n      return binding.projectGetSourceMapSync(this._nativeProject, filePath)\n    }\n\n    updateInfoSubscribe(aggregationMs: number) {\n      return subscribe<TurbopackResult<UpdateMessage>>(true, async (callback) =>\n        binding.projectUpdateInfoSubscribe(\n          this._nativeProject,\n          aggregationMs,\n          callback\n        )\n      )\n    }\n\n    shutdown(): Promise<void> {\n      return binding.projectShutdown(this._nativeProject)\n    }\n\n    onExit(): Promise<void> {\n      return binding.projectOnExit(this._nativeProject)\n    }\n  }\n\n  class EndpointImpl implements Endpoint {\n    private readonly _nativeEndpoint: { __napiType: 'Endpoint' }\n\n    constructor(nativeEndpoint: { __napiType: 'Endpoint' }) {\n      this._nativeEndpoint = nativeEndpoint\n    }\n\n    async writeToDisk(): Promise<TurbopackResult<WrittenEndpoint>> {\n      return await withErrorCause(\n        () =>\n          binding.endpointWriteToDisk(this._nativeEndpoint) as Promise<\n            TurbopackResult<WrittenEndpoint>\n          >\n      )\n    }\n\n    async clientChanged(): Promise<AsyncIterableIterator<TurbopackResult<{}>>> {\n      const clientSubscription = subscribe<TurbopackResult>(\n        false,\n        async (callback) =>\n          binding.endpointClientChangedSubscribe(\n            await this._nativeEndpoint,\n            callback\n          )\n      )\n      await clientSubscription.next()\n      return clientSubscription\n    }\n\n    async serverChanged(\n      includeIssues: boolean\n    ): Promise<AsyncIterableIterator<TurbopackResult<{}>>> {\n      const serverSubscription = subscribe<TurbopackResult>(\n        false,\n        async (callback) =>\n          binding.endpointServerChangedSubscribe(\n            await this._nativeEndpoint,\n            includeIssues,\n            callback\n          )\n      )\n      await serverSubscription.next()\n      return serverSubscription\n    }\n  }\n\n  /**\n   * Returns a new copy of next.js config object to avoid mutating the original.\n   *\n   * Also it does some augmentation to the configuration as well, for example set the\n   * turbopack's rules if `experimental.reactCompilerOptions` is set.\n   */\n  function augmentNextConfig(\n    originalNextConfig: NextConfigComplete,\n    projectPath: string\n  ): Record<string, any> {\n    let nextConfig = { ...(originalNextConfig as any) }\n\n    const reactCompilerOptions = nextConfig.experimental?.reactCompiler\n\n    // It is not easy to set the rules inside of rust as resolving, and passing the context identical to the webpack\n    // config is bit hard, also we can reuse same codes between webpack config in here.\n    if (reactCompilerOptions) {\n      const ruleKeys = ['*.ts', '*.js', '*.jsx', '*.tsx']\n      if (\n        Object.keys(nextConfig?.turbopack?.rules ?? []).some((key) =>\n          ruleKeys.includes(key)\n        )\n      ) {\n        Log.warn(\n          `The React Compiler cannot be enabled automatically because 'turbopack.rules' contains a rule for '*.ts', '*.js', '*.jsx', and '*.tsx'. Remove this rule, or add 'babel-loader' and 'babel-plugin-react-compiler' to the Turbopack configuration manually.`\n        )\n      } else {\n        if (!nextConfig.turbopack) {\n          nextConfig.turbopack = {}\n        }\n\n        if (!nextConfig.turbopack.rules) {\n          nextConfig.turbopack.rules = {}\n        }\n\n        for (const key of ['*.ts', '*.js', '*.jsx', '*.tsx']) {\n          nextConfig.turbopack.rules[key] = {\n            browser: {\n              foreign: false,\n              loaders: [\n                getReactCompilerLoader(\n                  originalNextConfig.experimental.reactCompiler,\n                  projectPath,\n                  nextConfig.dev,\n                  false,\n                  undefined\n                ),\n              ],\n            },\n          }\n        }\n      }\n    }\n\n    return nextConfig\n  }\n\n  async function serializeNextConfig(\n    nextConfig: NextConfigComplete,\n    projectPath: string\n  ): Promise<string> {\n    // Avoid mutating the existing `nextConfig` object.\n    let nextConfigSerializable = augmentNextConfig(nextConfig, projectPath)\n\n    nextConfigSerializable.generateBuildId =\n      await nextConfig.generateBuildId?.()\n\n    // TODO: these functions takes arguments, have to be supported in a different way\n    nextConfigSerializable.exportPathMap = {}\n    nextConfigSerializable.webpack = nextConfig.webpack && {}\n\n    if (nextConfigSerializable.experimental?.turbo?.rules) {\n      ensureLoadersHaveSerializableOptions(\n        nextConfigSerializable.turbopack?.rules\n      )\n    }\n\n    nextConfigSerializable.modularizeImports =\n      nextConfigSerializable.modularizeImports\n        ? Object.fromEntries(\n            Object.entries<any>(nextConfigSerializable.modularizeImports).map(\n              ([mod, config]) => [\n                mod,\n                {\n                  ...config,\n                  transform:\n                    typeof config.transform === 'string'\n                      ? config.transform\n                      : Object.entries(config.transform).map(([key, value]) => [\n                          key,\n                          value,\n                        ]),\n                },\n              ]\n            )\n          )\n        : undefined\n\n    // loaderFile is an absolute path, we need it to be relative for turbopack.\n    if (nextConfigSerializable.images.loaderFile) {\n      nextConfigSerializable.images = {\n        ...nextConfig.images,\n        loaderFile:\n          './' + path.relative(projectPath, nextConfig.images.loaderFile),\n      }\n    }\n\n    return JSON.stringify(nextConfigSerializable, null, 2)\n  }\n\n  function ensureLoadersHaveSerializableOptions(\n    turbopackRules: Record<string, TurbopackRuleConfigItemOrShortcut>\n  ) {\n    for (const [glob, rule] of Object.entries(turbopackRules)) {\n      if (Array.isArray(rule)) {\n        checkLoaderItems(rule, glob)\n      } else {\n        checkConfigItem(rule, glob)\n      }\n    }\n\n    function checkConfigItem(rule: TurbopackRuleConfigItem, glob: string) {\n      if (!rule) return\n      if ('loaders' in rule) {\n        checkLoaderItems((rule as TurbopackRuleConfigItemOptions).loaders, glob)\n      } else {\n        for (const key in rule) {\n          const inner = rule[key]\n          if (typeof inner === 'object' && inner) {\n            checkConfigItem(inner, glob)\n          }\n        }\n      }\n    }\n\n    function checkLoaderItems(\n      loaderItems: TurbopackLoaderItem[],\n      glob: string\n    ) {\n      for (const loaderItem of loaderItems) {\n        if (\n          typeof loaderItem !== 'string' &&\n          !isDeepStrictEqual(loaderItem, JSON.parse(JSON.stringify(loaderItem)))\n        ) {\n          throw new Error(\n            `loader ${loaderItem.loader} for match \"${glob}\" does not have serializable options. Ensure that options passed are plain JavaScript objects and values.`\n          )\n        }\n      }\n    }\n  }\n\n  function napiEntrypointsToRawEntrypoints(\n    entrypoints: TurbopackResult<NapiEntrypoints>\n  ): TurbopackResult<RawEntrypoints> {\n    const routes = new Map()\n    for (const { pathname, ...nativeRoute } of entrypoints.routes) {\n      let route: Route\n      const routeType = nativeRoute.type\n      switch (routeType) {\n        case 'page':\n          route = {\n            type: 'page',\n            htmlEndpoint: new EndpointImpl(nativeRoute.htmlEndpoint),\n            dataEndpoint: new EndpointImpl(nativeRoute.dataEndpoint),\n          }\n          break\n        case 'page-api':\n          route = {\n            type: 'page-api',\n            endpoint: new EndpointImpl(nativeRoute.endpoint),\n          }\n          break\n        case 'app-page':\n          route = {\n            type: 'app-page',\n            pages: nativeRoute.pages.map((page) => ({\n              originalName: page.originalName,\n              htmlEndpoint: new EndpointImpl(page.htmlEndpoint),\n              rscEndpoint: new EndpointImpl(page.rscEndpoint),\n            })),\n          }\n          break\n        case 'app-route':\n          route = {\n            type: 'app-route',\n            originalName: nativeRoute.originalName,\n            endpoint: new EndpointImpl(nativeRoute.endpoint),\n          }\n          break\n        case 'conflict':\n          route = {\n            type: 'conflict',\n          }\n          break\n        default:\n          const _exhaustiveCheck: never = routeType\n          invariant(\n            nativeRoute,\n            () => `Unknown route type: ${_exhaustiveCheck}`\n          )\n      }\n      routes.set(pathname, route)\n    }\n    const napiMiddlewareToMiddleware = (middleware: NapiMiddleware) => ({\n      endpoint: new EndpointImpl(middleware.endpoint),\n      runtime: middleware.runtime,\n      matcher: middleware.matcher,\n    })\n    const middleware = entrypoints.middleware\n      ? napiMiddlewareToMiddleware(entrypoints.middleware)\n      : undefined\n    const napiInstrumentationToInstrumentation = (\n      instrumentation: NapiInstrumentation\n    ) => ({\n      nodeJs: new EndpointImpl(instrumentation.nodeJs),\n      edge: new EndpointImpl(instrumentation.edge),\n    })\n    const instrumentation = entrypoints.instrumentation\n      ? napiInstrumentationToInstrumentation(entrypoints.instrumentation)\n      : undefined\n\n    return {\n      routes,\n      middleware,\n      instrumentation,\n      pagesDocumentEndpoint: new EndpointImpl(\n        entrypoints.pagesDocumentEndpoint\n      ),\n      pagesAppEndpoint: new EndpointImpl(entrypoints.pagesAppEndpoint),\n      pagesErrorEndpoint: new EndpointImpl(entrypoints.pagesErrorEndpoint),\n      issues: entrypoints.issues,\n      diagnostics: entrypoints.diagnostics,\n    }\n  }\n\n  return async function createProject(\n    options: ProjectOptions,\n    turboEngineOptions\n  ) {\n    return new ProjectImpl(\n      await binding.projectNew(\n        await rustifyProjectOptions(options),\n        turboEngineOptions || {}\n      )\n    )\n  }\n}\n\nasync function loadWasm(importPath = '') {\n  if (wasmBindings) {\n    return wasmBindings\n  }\n\n  let attempts = []\n  for (let pkg of ['@next/swc-wasm-nodejs', '@next/swc-wasm-web']) {\n    try {\n      let pkgPath = pkg\n\n      if (importPath) {\n        // the import path must be exact when not in node_modules\n        pkgPath = path.join(importPath, pkg, 'wasm.js')\n      }\n      let bindings: RawWasmBindings = await import(\n        pathToFileURL(pkgPath).toString()\n      )\n      if (pkg === '@next/swc-wasm-web') {\n        bindings = await bindings.default!()\n      }\n      infoLog('next-swc build: wasm build @next/swc-wasm-web')\n\n      // Note wasm binary does not support async intefaces yet, all async\n      // interface coereces to sync interfaces.\n      wasmBindings = {\n        css: {\n          lightning: {\n            transform: function (_options: any) {\n              throw new Error(\n                '`css.lightning.transform` is not supported by the wasm bindings.'\n              )\n            },\n            transformStyleAttr: function (_options: any) {\n              throw new Error(\n                '`css.lightning.transformStyleAttr` is not supported by the wasm bindings.'\n              )\n            },\n          },\n        },\n        isWasm: true,\n        transform(src: string, options: any) {\n          // TODO: we can remove fallback to sync interface once new stable version of next-swc gets published (current v12.2)\n          return bindings?.transform\n            ? bindings.transform(src.toString(), options)\n            : Promise.resolve(bindings.transformSync(src.toString(), options))\n        },\n        transformSync(src: string, options: any) {\n          return bindings.transformSync(src.toString(), options)\n        },\n        minify(src: string, options: any) {\n          return bindings?.minify\n            ? bindings.minify(src.toString(), options)\n            : Promise.resolve(bindings.minifySync(src.toString(), options))\n        },\n        minifySync(src: string, options: any) {\n          return bindings.minifySync(src.toString(), options)\n        },\n        parse(src: string, options: any) {\n          return bindings?.parse\n            ? bindings.parse(src.toString(), options)\n            : Promise.resolve(bindings.parseSync(src.toString(), options))\n        },\n        getTargetTriple() {\n          return undefined\n        },\n        turbo: {\n          createProject: function (\n            _options: ProjectOptions,\n            _turboEngineOptions?: TurboEngineOptions | undefined\n          ): Promise<Project> {\n            throw new Error(\n              '`turbo.createProject` is not supported by the wasm bindings.'\n            )\n          },\n          startTurbopackTraceServer: function (_traceFilePath: string): void {\n            throw new Error(\n              '`turbo.startTurbopackTraceServer` is not supported by the wasm bindings.'\n            )\n          },\n        },\n        mdx: {\n          compile(src: string, options: any) {\n            return bindings.mdxCompile(src, getMdxOptions(options))\n          },\n          compileSync(src: string, options: any) {\n            return bindings.mdxCompileSync(src, getMdxOptions(options))\n          },\n        },\n        reactCompiler: {\n          isReactCompilerRequired(_filename: string) {\n            return Promise.resolve(true)\n          },\n        },\n      }\n      return wasmBindings\n    } catch (e: any) {\n      // Only log attempts for loading wasm when loading as fallback\n      if (importPath) {\n        if (e?.code === 'ERR_MODULE_NOT_FOUND') {\n          attempts.push(`Attempted to load ${pkg}, but it was not installed`)\n        } else {\n          attempts.push(\n            `Attempted to load ${pkg}, but an error occurred: ${e.message ?? e}`\n          )\n        }\n      }\n    }\n  }\n\n  throw attempts\n}\n\nfunction loadNative(importPath?: string) {\n  if (nativeBindings) {\n    return nativeBindings\n  }\n\n  const customBindings: RawBindings = !!__INTERNAL_CUSTOM_TURBOPACK_BINDINGS\n    ? require(__INTERNAL_CUSTOM_TURBOPACK_BINDINGS)\n    : null\n  let bindings: RawBindings = customBindings\n  let attempts: any[] = []\n\n  const NEXT_TEST_NATIVE_DIR = process.env.NEXT_TEST_NATIVE_DIR\n  for (const triple of triples) {\n    if (NEXT_TEST_NATIVE_DIR) {\n      try {\n        // Use the binary directly to skip `pnpm pack` for testing as it's slow because of the large native binary.\n        bindings = require(\n          `${NEXT_TEST_NATIVE_DIR}/next-swc.${triple.platformArchABI}.node`\n        )\n        infoLog(\n          'next-swc build: local built @next/swc from NEXT_TEST_NATIVE_DIR'\n        )\n        break\n      } catch (e) {}\n    } else {\n      try {\n        bindings = require(\n          `@next/swc/native/next-swc.${triple.platformArchABI}.node`\n        )\n        infoLog('next-swc build: local built @next/swc')\n        break\n      } catch (e) {}\n    }\n  }\n\n  if (!bindings) {\n    for (const triple of triples) {\n      let pkg = importPath\n        ? path.join(\n            importPath,\n            `@next/swc-${triple.platformArchABI}`,\n            `next-swc.${triple.platformArchABI}.node`\n          )\n        : `@next/swc-${triple.platformArchABI}`\n      try {\n        bindings = require(pkg)\n        if (!importPath) {\n          checkVersionMismatch(require(`${pkg}/package.json`))\n        }\n        break\n      } catch (e: any) {\n        if (e?.code === 'MODULE_NOT_FOUND') {\n          attempts.push(`Attempted to load ${pkg}, but it was not installed`)\n        } else {\n          attempts.push(\n            `Attempted to load ${pkg}, but an error occurred: ${e.message ?? e}`\n          )\n        }\n        lastNativeBindingsLoadErrorCode = e?.code ?? 'unknown'\n      }\n    }\n  }\n\n  if (bindings) {\n    nativeBindings = {\n      isWasm: false,\n      transform(src: string, options: any) {\n        const isModule =\n          typeof src !== 'undefined' &&\n          typeof src !== 'string' &&\n          !Buffer.isBuffer(src)\n        options = options || {}\n\n        if (options?.jsc?.parser) {\n          options.jsc.parser.syntax = options.jsc.parser.syntax ?? 'ecmascript'\n        }\n\n        return bindings.transform(\n          isModule ? JSON.stringify(src) : src,\n          isModule,\n          toBuffer(options)\n        )\n      },\n\n      transformSync(src: string, options: any) {\n        if (typeof src === 'undefined') {\n          throw new Error(\n            \"transformSync doesn't implement reading the file from filesystem\"\n          )\n        } else if (Buffer.isBuffer(src)) {\n          throw new Error(\n            \"transformSync doesn't implement taking the source code as Buffer\"\n          )\n        }\n        const isModule = typeof src !== 'string'\n        options = options || {}\n\n        if (options?.jsc?.parser) {\n          options.jsc.parser.syntax = options.jsc.parser.syntax ?? 'ecmascript'\n        }\n\n        return bindings.transformSync(\n          isModule ? JSON.stringify(src) : src,\n          isModule,\n          toBuffer(options)\n        )\n      },\n\n      minify(src: string, options: any) {\n        return bindings.minify(toBuffer(src), toBuffer(options ?? {}))\n      },\n\n      minifySync(src: string, options: any) {\n        return bindings.minifySync(toBuffer(src), toBuffer(options ?? {}))\n      },\n\n      parse(src: string, options: any) {\n        return bindings.parse(src, toBuffer(options ?? {}))\n      },\n\n      getTargetTriple: bindings.getTargetTriple,\n      initCustomTraceSubscriber: bindings.initCustomTraceSubscriber,\n      teardownTraceSubscriber: bindings.teardownTraceSubscriber,\n      turbo: {\n        createProject: bindingToApi(customBindings ?? bindings, false),\n        startTurbopackTraceServer(traceFilePath) {\n          Log.warn(\n            'Turbopack trace server started. View trace at https://trace.nextjs.org'\n          )\n          ;(customBindings ?? bindings).startTurbopackTraceServer(traceFilePath)\n        },\n      },\n      mdx: {\n        compile(src: string, options: any) {\n          return bindings.mdxCompile(src, toBuffer(getMdxOptions(options)))\n        },\n        compileSync(src: string, options: any) {\n          bindings.mdxCompileSync(src, toBuffer(getMdxOptions(options)))\n        },\n      },\n      css: {\n        lightning: {\n          transform(transformOptions: any) {\n            return bindings.lightningCssTransform(transformOptions)\n          },\n          transformStyleAttr(transformAttrOptions: any) {\n            return bindings.lightningCssTransformStyleAttribute(\n              transformAttrOptions\n            )\n          },\n        },\n      },\n      reactCompiler: {\n        isReactCompilerRequired: (filename: string) => {\n          return bindings.isReactCompilerRequired(filename)\n        },\n      },\n    }\n    return nativeBindings\n  }\n\n  throw attempts\n}\n\n/// Build a mdx options object contains default values that\n/// can be parsed with serde_wasm_bindgen.\nfunction getMdxOptions(options: any = {}) {\n  return {\n    ...options,\n    development: options.development ?? false,\n    jsx: options.jsx ?? false,\n    mdxType: options.mdxType ?? 'commonMark',\n  }\n}\n\nfunction toBuffer(t: any) {\n  return Buffer.from(JSON.stringify(t))\n}\n\nexport async function isWasm(): Promise<boolean> {\n  let bindings = await loadBindings()\n  return bindings.isWasm\n}\n\nexport async function transform(src: string, options?: any): Promise<any> {\n  let bindings = await loadBindings()\n  return bindings.transform(src, options)\n}\n\nexport function transformSync(src: string, options?: any): any {\n  let bindings = loadBindingsSync()\n  return bindings.transformSync(src, options)\n}\n\nexport async function minify(\n  src: string,\n  options: any\n): Promise<{ code: string; map: any }> {\n  let bindings = await loadBindings()\n  return bindings.minify(src, options)\n}\n\nexport async function isReactCompilerRequired(\n  filename: string\n): Promise<boolean> {\n  let bindings = await loadBindings()\n  return bindings.reactCompiler.isReactCompilerRequired(filename)\n}\n\nexport async function parse(src: string, options: any): Promise<any> {\n  let bindings = await loadBindings()\n  let parserOptions = getParserOptions(options)\n  return bindings\n    .parse(src, parserOptions)\n    .then((astStr: any) => JSON.parse(astStr))\n}\n\nexport function getBinaryMetadata() {\n  let bindings\n  try {\n    bindings = loadNative()\n  } catch (e) {\n    // Suppress exceptions, this fn allows to fail to load native bindings\n  }\n\n  return {\n    target: bindings?.getTargetTriple?.(),\n  }\n}\n\n/**\n * Initialize trace subscriber to emit traces.\n *\n */\nexport function initCustomTraceSubscriber(traceFileName?: string) {\n  if (swcTraceFlushGuard) {\n    // Wasm binary doesn't support trace emission\n    let bindings = loadNative()\n    swcTraceFlushGuard = bindings.initCustomTraceSubscriber?.(traceFileName)\n  }\n}\n\nfunction once(fn: () => void): () => void {\n  let executed = false\n\n  return function (): void {\n    if (!executed) {\n      executed = true\n\n      fn()\n    }\n  }\n}\n\n/**\n * Teardown swc's trace subscriber if there's an initialized flush guard exists.\n *\n * This is workaround to amend behavior with process.exit\n * (https://github.com/vercel/next.js/blob/4db8c49cc31e4fc182391fae6903fb5ef4e8c66e/packages/next/bin/next.ts#L134=)\n * seems preventing napi's cleanup hook execution (https://github.com/swc-project/swc/blob/main/crates/node/src/util.rs#L48-L51=),\n *\n * instead parent process manually drops guard when process gets signal to exit.\n */\nexport const teardownTraceSubscriber = once(() => {\n  try {\n    let bindings = loadNative()\n    if (swcTraceFlushGuard) {\n      bindings.teardownTraceSubscriber?.(swcTraceFlushGuard)\n    }\n  } catch (e) {\n    // Suppress exceptions, this fn allows to fail to load native bindings\n  }\n})\n"], "names": ["path", "pathToFileURL", "arch", "platform", "platformArchTriples", "Log", "getParserOptions", "eventSwcLoadFailure", "patchIncorrectLockfile", "downloadNativeNextSwc", "downloadWasmSwc", "isDeepStrictEqual", "getDefineEnv", "getReactCompilerLoader", "TurbopackInternalError", "nextVersion", "process", "env", "__NEXT_VERSION", "Arch<PERSON>ame", "PlatformName", "infoLog", "args", "NEXT_PRIVATE_BUILD_WORKER", "DEBUG", "info", "getSupportedArchTriples", "darwin", "win32", "linux", "freebsd", "android", "arm64", "ia32", "filter", "triple", "abi", "x64", "arm", "triples", "supportedArchTriples", "targetTriple", "rawTargetTriple", "warn", "__INTERNAL_CUSTOM_TURBOPACK_BINDINGS", "checkVersionMismatch", "pkgData", "version", "knownDefaultWasmFallbackTriples", "lastNativeBindingsLoadErrorCode", "undefined", "nativeBindings", "wasmBindings", "downloadWasmPromise", "pendingBindings", "swcTraceFlushGuard", "downloadNativeBindingsPromise", "lockfilePatchPromise", "loadBindings", "useWasmBinary", "RUST_MIN_STACK", "stdout", "_handle", "setBlocking", "stderr", "Promise", "resolve", "_reject", "cur", "cwd", "catch", "console", "error", "attempts", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NEXT_DISABLE_SWC_WASM", "unsupportedPlatform", "some", "raw", "includes", "isWebContainer", "versions", "webcontainer", "shouldLoadWasmFallbackFirst", "fallback<PERSON><PERSON><PERSON>", "tryLoadWasmWithFallback", "loadNative", "a", "Array", "isArray", "every", "m", "tryLoadNativeWithFallback", "concat", "logLoadFailure", "nativeBindingsDirectory", "join", "dirname", "require", "map", "platformArchABI", "push", "bindings", "loadWasm", "wasm", "nativeBindingsErrorCode", "wasmDirectory", "attempt", "loadBindingsSync", "Error", "cause", "loggingLoadFailure", "triedWasm", "then", "finally", "exit", "createDefineEnv", "isTurbopack", "clientRouterFilters", "config", "dev", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "middlewareMatchers", "defineEnv", "client", "edge", "nodejs", "variant", "Object", "keys", "rustifyEnv", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "entries", "_", "value", "name", "bindingToApi", "binding", "_wasm", "cancel", "Cancel", "invariant", "never", "computeMessage", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fn", "nativeError", "createAndRecordTelemetry", "subscribe", "useBuffer", "nativeFunction", "buffer", "waiting", "canceled", "emitResult", "err", "reject", "item", "createIterator", "task", "length", "shift", "e", "rootTaskDispose", "iterator", "return", "done", "rustifyProjectOptions", "options", "nextConfig", "serializeNextConfig", "projectPath", "jsConfig", "JSON", "stringify", "rustifyPartialProjectOptions", "ProjectImpl", "constructor", "nativeProject", "_nativeProject", "update", "projectUpdate", "writeAllEntrypointsToDisk", "appDirOnly", "napiEndpoints", "projectWriteAllEntrypointsToDisk", "napiEntrypointsToRawEntrypoints", "entrypointsSubscribe", "subscription", "callback", "projectEntrypointsSubscribe", "entrypoints", "hmrEvents", "identifier", "projectHmrEvents", "hmrIdentifiersSubscribe", "projectHmrIdentifiersSubscribe", "traceSource", "stackFrame", "currentDirectoryFileUrl", "projectTraceSource", "getSourceForAsset", "filePath", "projectGetSourceForAsset", "getSourceMap", "projectGetSourceMap", "getSourceMapSync", "projectGetSourceMapSync", "updateInfoSubscribe", "aggregationMs", "projectUpdateInfoSubscribe", "shutdown", "projectShutdown", "onExit", "projectOnExit", "EndpointImpl", "nativeEndpoint", "_nativeEndpoint", "writeToDisk", "endpointWriteToDisk", "clientChanged", "clientSubscription", "endpointClientChangedSubscribe", "next", "serverChanged", "includeIssues", "serverSubscription", "endpointServerChangedSubscribe", "augmentNextConfig", "originalNextConfig", "reactCompilerOptions", "experimental", "reactCompiler", "rule<PERSON>eys", "turbopack", "rules", "key", "browser", "foreign", "loaders", "nextConfigSerializable", "generateBuildId", "exportPathMap", "webpack", "turbo", "ensureLoadersHaveSerializableOptions", "modularizeImports", "fromEntries", "mod", "transform", "images", "loaderFile", "relative", "turbopackRules", "glob", "rule", "checkLoaderItems", "checkConfigItem", "inner", "loaderItems", "loaderItem", "parse", "loader", "routes", "Map", "pathname", "nativeRoute", "route", "routeType", "type", "htmlEndpoint", "dataEndpoint", "endpoint", "pages", "page", "originalName", "rscEndpoint", "_exhaustiveCheck", "set", "napiMiddlewareToMiddleware", "middleware", "runtime", "matcher", "napiInstrumentationToInstrumentation", "instrumentation", "nodeJs", "pagesDocumentEndpoint", "pagesAppEndpoint", "pagesErrorEndpoint", "issues", "diagnostics", "createProject", "turboEngineOptions", "projectNew", "importPath", "pkg", "pkgPath", "toString", "default", "css", "lightning", "_options", "transformStyleAttr", "isWasm", "src", "transformSync", "minify", "minifySync", "parseSync", "getTargetTriple", "_turboEngineOptions", "startTurbopackTraceServer", "_traceFilePath", "mdx", "compile", "mdxCompile", "getMdxOptions", "compileSync", "mdxCompileSync", "isReactCompilerRequired", "_filename", "code", "message", "customBindings", "NEXT_TEST_NATIVE_DIR", "isModule", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "jsc", "parser", "syntax", "<PERSON><PERSON><PERSON><PERSON>", "initCustomTraceSubscriber", "teardownTraceSubscriber", "traceFilePath", "transformOptions", "lightningCssTransform", "transformAttrOptions", "lightningCssTransformStyleAttribute", "filename", "development", "jsx", "mdxType", "t", "from", "parserOptions", "astStr", "getBinaryMetadata", "target", "traceFileName", "once", "executed"], "mappings": "AAAA,0DAA0D,GAC1D,OAAOA,UAAU,OAAM;AACvB,SAASC,aAAa,QAAQ,MAAK;AACnC,SAASC,IAAI,EAAEC,QAAQ,QAAQ,KAAI;AACnC,SAASC,mBAAmB,QAAQ,sCAAqC;AACzE,YAAYC,SAAS,gBAAe;AACpC,SAASC,gBAAgB,QAAQ,YAAW;AAC5C,SAASC,mBAAmB,QAAQ,0CAAyC;AAC7E,SAASC,sBAAsB,QAAQ,qCAAoC;AAC3E,SAASC,qBAAqB,EAAEC,eAAe,QAAQ,yBAAwB;AAQ/E,SAASC,iBAAiB,QAAQ,OAAM;AACxC,SAEEC,YAAY,QACP,uCAAsC;AAC7C,SAASC,sBAAsB,QAAQ,6BAA4B;AAqBnE,SAASC,sBAAsB,QAAQ,mCAAkC;AAOzE,MAAMC,cAAcC,QAAQC,GAAG,CAACC,cAAc;AAE9C,MAAMC,WAAWjB;AACjB,MAAMkB,eAAejB;AAErB,SAASkB,QAAQ,GAAGC,IAAW;IAC7B,IAAIN,QAAQC,GAAG,CAACM,yBAAyB,EAAE;QACzC;IACF;IACA,IAAIP,QAAQC,GAAG,CAACO,KAAK,EAAE;QACrBnB,IAAIoB,IAAI,IAAIH;IACd;AACF;AAEA;;CAEC,GACD,OAAO,SAASI;IACd,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE,GAAG3B;IAEnD,OAAO;QACLuB;QACAC,OAAO;YACLI,OAAOJ,MAAMI,KAAK;YAClBC,MAAML,MAAMK,IAAI,CAACC,MAAM,CAAC,CAACC,SAAWA,OAAOC,GAAG,KAAK;YACnDC,KAAKT,MAAMS,GAAG,CAACH,MAAM,CAAC,CAACC,SAAWA,OAAOC,GAAG,KAAK;QACnD;QACAP,OAAO;YACL,mDAAmD;YACnDQ,KAAKR,MAAMQ,GAAG,CAACH,MAAM,CAAC,CAACC,SAAWA,OAAOC,GAAG,KAAK;YACjDJ,OAAOH,MAAMG,KAAK;YAClB,mGAAmG;YACnGM,KAAKT,MAAMS,GAAG;QAChB;QACA,sGAAsG;QACtGR,SAAS;YACPO,KAAKP,QAAQO,GAAG;QAClB;QACAN,SAAS;YACPC,OAAOD,QAAQC,KAAK;YACpBM,KAAKP,QAAQO,GAAG;QAClB;IACF;AACF;AAEA,MAAMC,UAAU,AAAC,CAAA;QAEMC,oCASCpC;IAVtB,MAAMoC,uBAAuBd;IAC7B,MAAMe,gBAAeD,qCAAAA,oBAAoB,CAACpB,aAAa,qBAAlCoB,kCAAoC,CAACrB,SAAS;IAEnE,oDAAoD;IACpD,IAAIsB,cAAc;QAChB,OAAOA;IACT;IAEA,yHAAyH;IACzH,qDAAqD;IACrD,IAAIC,mBAAkBtC,oCAAAA,mBAAmB,CAACgB,aAAa,qBAAjChB,iCAAmC,CAACe,SAAS;IAEnE,IAAIuB,iBAAiB;QACnBrC,IAAIsC,IAAI,CACN,CAAC,0CAA0C,EAAED,gBAAgB,0DAA0D,CAAC;IAE5H,OAAO;QACLrC,IAAIsC,IAAI,CACN,CAAC,kDAAkD,EAAEvB,aAAa,CAAC,EAAED,UAAU;IAEnF;IAEA,OAAO,EAAE;AACX,CAAA;AAEA,4EAA4E;AAC5E,yEAAyE;AACzE,gDAAgD;AAChD,kEAAkE;AAClE,EAAE;AACF,yEAAyE;AACzE,MAAMyB,uCACJ5B,QAAQC,GAAG,CAAC2B,oCAAoC;AAElD,SAASC,qBAAqBC,OAAY;IACxC,MAAMC,UAAUD,QAAQC,OAAO;IAE/B,IAAIA,WAAWA,YAAYhC,aAAa;QACtCV,IAAIsC,IAAI,CACN,CAAC,yCAAyC,EAAEI,QAAQ,qBAAqB,EAAEhC,YAAY,2BAA2B,CAAC;IAEvH;AACF;AAEA,iEAAiE;AACjE,0EAA0E;AAC1E,2DAA2D;AAC3D,yEAAyE;AACzE,+DAA+D;AAC/D,MAAMiC,kCAAkC;IACtC;IACA;IACA;IACA;IACA;CAGD;AAED,oFAAoF;AACpF,gGAAgG;AAChG,oGAAoG;AACpG,IAAIC,kCAIYC;AAChB,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC,gCAA2DN;AAE/D,OAAO,MAAMO,uBAAgD,CAAC,EAAC;AAE/D,OAAO,eAAeC,aACpBC,gBAAyB,KAAK;IAE9B,2FAA2F;IAC3F,IAAI,CAAC3C,QAAQC,GAAG,CAAC2C,cAAc,EAAE;QAC/B5C,QAAQC,GAAG,CAAC2C,cAAc,GAAG;IAC/B;IAEA,IAAIN,iBAAiB;QACnB,OAAOA;IACT;IAEA,iIAAiI;IACjI,qDAAqD;IACrD,uFAAuF;IACvF,IAAItC,QAAQ6C,MAAM,CAACC,OAAO,IAAI,MAAM;QAClC,aAAa;QACb9C,QAAQ6C,MAAM,CAACC,OAAO,CAACC,WAAW,oBAAlC/C,QAAQ6C,MAAM,CAACC,OAAO,CAACC,WAAW,MAAlC/C,QAAQ6C,MAAM,CAACC,OAAO,EAAe;IACvC;IACA,IAAI9C,QAAQgD,MAAM,CAACF,OAAO,IAAI,MAAM;QAClC,aAAa;QACb9C,QAAQgD,MAAM,CAACF,OAAO,CAACC,WAAW,oBAAlC/C,QAAQgD,MAAM,CAACF,OAAO,CAACC,WAAW,MAAlC/C,QAAQgD,MAAM,CAACF,OAAO,EAAe;IACvC;IAEAR,kBAAkB,IAAIW,QAAQ,OAAOC,SAASC;QAC5C,IAAI,CAACV,qBAAqBW,GAAG,EAAE;YAC7B,yDAAyD;YACzD,0CAA0C;YAC1CX,qBAAqBW,GAAG,GAAG5D,uBAAuBQ,QAAQqD,GAAG,IAAIC,KAAK,CACpEC,QAAQC,KAAK;QAEjB;QAEA,IAAIC,WAAkB,EAAE;QACxB,MAAMC,sBAAsB1D,QAAQC,GAAG,CAAC0D,qBAAqB;QAC7D,MAAMC,sBAAsBrC,QAAQsC,IAAI,CACtC,CAAC1C,SACC,CAAC,EAACA,0BAAAA,OAAQ2C,GAAG,KAAI9B,gCAAgC+B,QAAQ,CAAC5C,OAAO2C,GAAG;QAExE,MAAME,iBAAiBhE,QAAQiE,QAAQ,CAACC,YAAY;QACpD,yEAAyE;QACzE,yFAAyF;QACzF,MAAMC,8BACJ,AAAC,CAACT,uBAAuBf,iBACzBiB,uBACAI;QAEF,IAAI,CAACJ,uBAAuBjB,eAAe;YACzCtD,IAAIsC,IAAI,CACN,CAAC,mEAAmE,EAAEvB,aAAa,CAAC,EAAED,SAAS,qBAAqB,CAAC;QAEzH;QAEA,IAAIgE,6BAA6B;YAC/BlC,kCAAkC;YAClC,MAAMmC,mBAAmB,MAAMC,wBAAwBZ;YACvD,IAAIW,kBAAkB;gBACpB,OAAOlB,QAAQkB;YACjB;QACF;QAEA,4CAA4C;QAC5C,EAAE;QACF,kEAAkE;QAClE,0GAA0G;QAC1G,gHAAgH;QAChH,kHAAkH;QAClH,kDAAkD;QAClD,uDAAuD;QACvD,IAAI;YACF,OAAOlB,QAAQoB;QACjB,EAAE,OAAOC,GAAG;YACV,IACEC,MAAMC,OAAO,CAACF,MACdA,EAAEG,KAAK,CAAC,CAACC,IAAMA,EAAEZ,QAAQ,CAAC,0BAC1B;gBACA,IAAIK,mBAAmB,MAAMQ,0BAA0BnB;gBAEvD,IAAIW,kBAAkB;oBACpB,OAAOlB,QAAQkB;gBACjB;YACF;YAEAX,WAAWA,SAASoB,MAAM,CAACN;QAC7B;QAEA,+EAA+E;QAC/E,IAAI,CAACJ,+BAA+B,CAACT,qBAAqB;YACxD,MAAMU,mBAAmB,MAAMC,wBAAwBZ;YACvD,IAAIW,kBAAkB;gBACpB,OAAOlB,QAAQkB;YACjB;QACF;QAEAU,eAAerB,UAAU;IAC3B;IACA,OAAOnB;AACT;AAEA,eAAesC,0BAA0BnB,QAAuB;IAC9D,MAAMsB,0BAA0B/F,KAAKgG,IAAI,CACvChG,KAAKiG,OAAO,CAACC,QAAQhC,OAAO,CAAC,uBAC7B;IAGF,IAAI,CAACV,+BAA+B;QAClCA,gCAAgC/C,sBAC9BM,aACAgF,yBACAxD,QAAQ4D,GAAG,CAAC,CAAChE,SAAgBA,OAAOiE,eAAe;IAEvD;IACA,MAAM5C;IAEN,IAAI;QACF,OAAO8B,WAAWS;IACpB,EAAE,OAAOR,GAAQ;QACfd,SAAS4B,IAAI,IAAI,EAAE,CAACR,MAAM,CAACN;IAC7B;IAEA,OAAOrC;AACT;AAEA,eAAemC,wBAAwBZ,QAAe;IACpD,IAAI;QACF,IAAI6B,WAAW,MAAMC,SAAS;QAC9B,sDAAsD;QACtDhG,oBAAoB;YAClBiG,MAAM;YACNC,yBAAyBxD;QAC3B;QACA,OAAOqD;IACT,EAAE,OAAOf,GAAQ;QACfd,SAAS4B,IAAI,IAAI,EAAE,CAACR,MAAM,CAACN;IAC7B;IAEA,IAAI;QACF,2DAA2D;QAC3D,+DAA+D;QAC/D,sEAAsE;QACtE,sDAAsD;QACtD,MAAMmB,gBAAgB1G,KAAKgG,IAAI,CAC7BhG,KAAKiG,OAAO,CAACC,QAAQhC,OAAO,CAAC,uBAC7B;QAEF,IAAI,CAACb,qBAAqB;YACxBA,sBAAsB3C,gBAAgBK,aAAa2F;QACrD;QACA,MAAMrD;QACN,IAAIiD,WAAW,MAAMC,SAASG;QAC9B,sDAAsD;QACtDnG,oBAAoB;YAClBiG,MAAM;YACNC,yBAAyBxD;QAC3B;QAEA,4CAA4C;QAC5C,sCAAsC;QACtC,KAAK,MAAM0D,WAAWlC,SAAU;YAC9BpE,IAAIsC,IAAI,CAACgE;QACX;QACA,OAAOL;IACT,EAAE,OAAOf,GAAQ;QACfd,SAAS4B,IAAI,IAAI,EAAE,CAACR,MAAM,CAACN;IAC7B;AACF;AAEA,SAASqB;IACP,IAAInC,WAAkB,EAAE;IACxB,IAAI;QACF,OAAOa;IACT,EAAE,OAAOC,GAAG;QACVd,WAAWA,SAASoB,MAAM,CAACN;IAC7B;IAEA,wDAAwD;IACxD,SAAS;IACT,IAAInC,cAAc;QAChB,OAAOA;IACT;IAEA0C,eAAerB;IACf,MAAM,qBAAyD,CAAzD,IAAIoC,MAAM,2BAA2B;QAAEC,OAAOrC;IAAS,IAAvD,qBAAA;eAAA;oBAAA;sBAAA;IAAwD;AAChE;AAEA,IAAIsC,qBAAqB;AAEzB,SAASjB,eAAerB,QAAa,EAAEuC,YAAY,KAAK;IACtD,4DAA4D;IAC5D,IAAID,oBAAoB;IACxBA,qBAAqB;IAErB,KAAK,IAAIJ,WAAWlC,SAAU;QAC5BpE,IAAIsC,IAAI,CAACgE;IACX;IAEA,sDAAsD;IACtDpG,oBAAoB;QAClBiG,MAAMQ,YAAY,WAAW9D;QAC7BuD,yBAAyBxD;IAC3B,GACGgE,IAAI,CAAC,IAAMxD,qBAAqBW,GAAG,IAAIH,QAAQC,OAAO,IACtDgD,OAAO,CAAC;QACP7G,IAAImE,KAAK,CACP,CAAC,8BAA8B,EAAEpD,aAAa,CAAC,EAAED,SAAS,yEAAyE,CAAC;QAEtIH,QAAQmG,IAAI,CAAC;IACf;AACJ;AAIA,OAAO,SAASC,gBAAgB,EAC9BC,WAAW,EACXC,mBAAmB,EACnBC,MAAM,EACNC,GAAG,EACHC,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,kBAAkB,EAInB;IACC,IAAIC,YAAuB;QACzBC,QAAQ,EAAE;QACVC,MAAM,EAAE;QACRC,QAAQ,EAAE;IACZ;IAEA,KAAK,MAAMC,WAAWC,OAAOC,IAAI,CAACN,WAA0C;QAC1EA,SAAS,CAACI,QAAQ,GAAGG,WACnBxH,aAAa;YACXyG;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAU,UAAUJ,YAAY;YACtBK,cAAcL,YAAY;YAC1BM,yBAAyBN,YAAY,YAAYA,YAAY;YAC7DO,cAAcP,YAAY;YAC1BL;QACF;IAEJ;IAEA,OAAOC;AACT;AAEA,SAASO,WAAWnH,GAA2B;IAC7C,OAAOiH,OAAOO,OAAO,CAACxH,KACnBiB,MAAM,CAAC,CAAC,CAACwG,GAAGC,MAAM,GAAKA,SAAS,MAChCxC,GAAG,CAAC,CAAC,CAACyC,MAAMD,MAAM,GAAM,CAAA;YACvBC;YACAD;QACF,CAAA;AACJ;AAEA,mCAAmC;AACnC,SAASE,aACPC,OAAoB,EACpBC,KAAc;IA0Dd,MAAMC,SAAS,IAAK,MAAMC,eAAepC;IAAO;IAEhD;;GAEC,GACD,SAASqC,UACPC,KAAY,EACZC,cAAoC;QAEpC,MAAM,qBAAgD,CAAhD,IAAIvC,MAAM,CAAC,WAAW,EAAEuC,eAAeD,QAAQ,GAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAA+C;IACvD;IAEA,eAAeE,eAAkBC,EAAoB;QACnD,IAAI;YACF,OAAO,MAAMA;QACf,EAAE,OAAOC,aAAkB;YACzB,MAAMzI,uBAAuB0I,wBAAwB,CAACD;QACxD;IACF;IAEA;;;;;GAKC,GACD,SAASE,UACPC,SAAkB,EAClBC,cAEiE;QAKjE,mEAAmE;QACnE,wCAAwC;QACxC,IAAIC,SAAuB,EAAE;QAC7B,sEAAsE;QACtE,qDAAqD;QACrD,IAAIC;QAMJ,IAAIC,WAAW;QAEf,0EAA0E;QAC1E,2EAA2E;QAC3E,2BAA2B;QAC3B,SAASC,WAAWC,GAAsB,EAAErB,KAAoB;YAC9D,IAAIkB,SAAS;gBACX,IAAI,EAAE3F,OAAO,EAAE+F,MAAM,EAAE,GAAGJ;gBAC1BA,UAAU3G;gBACV,IAAI8G,KAAKC,OAAOD;qBACX9F,QAAQyE;YACf,OAAO;gBACL,MAAMuB,OAAO;oBAAEF;oBAAKrB;gBAAM;gBAC1B,IAAIe,WAAWE,OAAOvD,IAAI,CAAC6D;qBACtBN,MAAM,CAAC,EAAE,GAAGM;YACnB;QACF;QAEA,gBAAgBC;YACd,MAAMC,OAAO,MAAMf,eAAkD,IACnEM,eAAeI;YAEjB,IAAI;gBACF,MAAO,CAACD,SAAU;oBAChB,IAAIF,OAAOS,MAAM,GAAG,GAAG;wBACrB,MAAMH,OAAON,OAAOU,KAAK;wBACzB,IAAIJ,KAAKF,GAAG,EAAE,MAAME,KAAKF,GAAG;wBAC5B,MAAME,KAAKvB,KAAK;oBAClB,OAAO;wBACL,wCAAwC;wBACxC,MAAM,IAAI1E,QAAW,CAACC,SAAS+F;4BAC7BJ,UAAU;gCAAE3F;gCAAS+F;4BAAO;wBAC9B;oBACF;gBACF;YACF,EAAE,OAAOM,GAAG;gBACV,IAAIA,MAAMvB,QAAQ;gBAClB,IAAIuB,aAAa1D,OAAO;oBACtB,MAAM/F,uBAAuB0I,wBAAwB,CAACe;gBACxD;gBACA,MAAMA;YACR,SAAU;gBACR,IAAIH,MAAM;oBACRtB,QAAQ0B,eAAe,CAACJ;gBAC1B;YACF;QACF;QAEA,MAAMK,WAAWN;QACjBM,SAASC,MAAM,GAAG;YAChBZ,WAAW;YACX,IAAID,SAASA,QAAQI,MAAM,CAACjB;YAC5B,OAAO;gBAAEL,OAAOzF;gBAAWyH,MAAM;YAAK;QACxC;QACA,OAAOF;IACT;IAEA,eAAeG,sBACbC,OAAuB;QAEvB,OAAO;YACL,GAAGA,OAAO;YACVC,YAAY,MAAMC,oBAChBF,QAAQC,UAAU,EAClBD,QAAQG,WAAW;YAErBC,UAAUC,KAAKC,SAAS,CAACN,QAAQI,QAAQ;YACzChK,KAAKmH,WAAWyC,QAAQ5J,GAAG;QAC7B;IACF;IAEA,eAAemK,6BACbP,OAAgC;QAEhC,OAAO;YACL,GAAGA,OAAO;YACVC,YACED,QAAQC,UAAU,IACjB,MAAMC,oBAAoBF,QAAQC,UAAU,EAAED,QAAQG,WAAW;YACpEC,UAAUJ,QAAQI,QAAQ,IAAIC,KAAKC,SAAS,CAACN,QAAQI,QAAQ;YAC7DhK,KAAK4J,QAAQ5J,GAAG,IAAImH,WAAWyC,QAAQ5J,GAAG;QAC5C;IACF;IAEA,MAAMoK;QAGJC,YAAYC,aAAwC,CAAE;YACpD,IAAI,CAACC,cAAc,GAAGD;QACxB;QAEA,MAAME,OAAOZ,OAAgC,EAAE;YAC7C,MAAMxB,eAAe,UACnBP,QAAQ4C,aAAa,CACnB,IAAI,CAACF,cAAc,EACnB,MAAMJ,6BAA6BP;QAGzC;QAEA,MAAMc,0BACJC,UAAmB,EACuB;YAC1C,OAAO,MAAMvC,eAAe;gBAC1B,MAAMwC,gBAAiB,MAAM/C,QAAQgD,gCAAgC,CACnE,IAAI,CAACN,cAAc,EACnBI;gBAGF,OAAOG,gCAAgCF;YACzC;QACF;QAEAG,uBAAuB;YACrB,MAAMC,eAAexC,UACnB,OACA,OAAOyC,WACLpD,QAAQqD,2BAA2B,CAAC,IAAI,CAACX,cAAc,EAAEU;YAE7D,OAAO,AAAC;gBACN,WAAW,MAAME,eAAeH,aAAc;oBAC5C,MAAMF,gCAAgCK;gBACxC;YACF;QACF;QAEAC,UAAUC,UAAkB,EAAE;YAC5B,OAAO7C,UAAmC,MAAM,OAAOyC,WACrDpD,QAAQyD,gBAAgB,CAAC,IAAI,CAACf,cAAc,EAAEc,YAAYJ;QAE9D;QAEAM,0BAA0B;YACxB,OAAO/C,UACL,OACA,OAAOyC,WACLpD,QAAQ2D,8BAA8B,CAAC,IAAI,CAACjB,cAAc,EAAEU;QAElE;QAEAQ,YACEC,UAA+B,EAC/BC,uBAA+B,EACM;YACrC,OAAO9D,QAAQ+D,kBAAkB,CAC/B,IAAI,CAACrB,cAAc,EACnBmB,YACAC;QAEJ;QAEAE,kBAAkBC,QAAgB,EAA0B;YAC1D,OAAOjE,QAAQkE,wBAAwB,CAAC,IAAI,CAACxB,cAAc,EAAEuB;QAC/D;QAEAE,aAAaF,QAAgB,EAA0B;YACrD,OAAOjE,QAAQoE,mBAAmB,CAAC,IAAI,CAAC1B,cAAc,EAAEuB;QAC1D;QAEAI,iBAAiBJ,QAAgB,EAAiB;YAChD,OAAOjE,QAAQsE,uBAAuB,CAAC,IAAI,CAAC5B,cAAc,EAAEuB;QAC9D;QAEAM,oBAAoBC,aAAqB,EAAE;YACzC,OAAO7D,UAA0C,MAAM,OAAOyC,WAC5DpD,QAAQyE,0BAA0B,CAChC,IAAI,CAAC/B,cAAc,EACnB8B,eACApB;QAGN;QAEAsB,WAA0B;YACxB,OAAO1E,QAAQ2E,eAAe,CAAC,IAAI,CAACjC,cAAc;QACpD;QAEAkC,SAAwB;YACtB,OAAO5E,QAAQ6E,aAAa,CAAC,IAAI,CAACnC,cAAc;QAClD;IACF;IAEA,MAAMoC;QAGJtC,YAAYuC,cAA0C,CAAE;YACtD,IAAI,CAACC,eAAe,GAAGD;QACzB;QAEA,MAAME,cAAyD;YAC7D,OAAO,MAAM1E,eACX,IACEP,QAAQkF,mBAAmB,CAAC,IAAI,CAACF,eAAe;QAItD;QAEA,MAAMG,gBAAqE;YACzE,MAAMC,qBAAqBzE,UACzB,OACA,OAAOyC,WACLpD,QAAQqF,8BAA8B,CACpC,MAAM,IAAI,CAACL,eAAe,EAC1B5B;YAGN,MAAMgC,mBAAmBE,IAAI;YAC7B,OAAOF;QACT;QAEA,MAAMG,cACJC,aAAsB,EAC+B;YACrD,MAAMC,qBAAqB9E,UACzB,OACA,OAAOyC,WACLpD,QAAQ0F,8BAA8B,CACpC,MAAM,IAAI,CAACV,eAAe,EAC1BQ,eACApC;YAGN,MAAMqC,mBAAmBH,IAAI;YAC7B,OAAOG;QACT;IACF;IAEA;;;;;GAKC,GACD,SAASE,kBACPC,kBAAsC,EACtC1D,WAAmB;YAIUF;QAF7B,IAAIA,aAAa;YAAE,GAAI4D,kBAAkB;QAAS;QAElD,MAAMC,wBAAuB7D,2BAAAA,WAAW8D,YAAY,qBAAvB9D,yBAAyB+D,aAAa;QAEnE,gHAAgH;QAChH,mFAAmF;QACnF,IAAIF,sBAAsB;gBAGV7D;YAFd,MAAMgE,WAAW;gBAAC;gBAAQ;gBAAQ;gBAAS;aAAQ;YACnD,IACE5G,OAAOC,IAAI,CAAC2C,CAAAA,+BAAAA,wBAAAA,WAAYiE,SAAS,qBAArBjE,sBAAuBkE,KAAK,KAAI,EAAE,EAAEnK,IAAI,CAAC,CAACoK,MACpDH,SAAS/J,QAAQ,CAACkK,OAEpB;gBACA5O,IAAIsC,IAAI,CACN,CAAC,yPAAyP,CAAC;YAE/P,OAAO;gBACL,IAAI,CAACmI,WAAWiE,SAAS,EAAE;oBACzBjE,WAAWiE,SAAS,GAAG,CAAC;gBAC1B;gBAEA,IAAI,CAACjE,WAAWiE,SAAS,CAACC,KAAK,EAAE;oBAC/BlE,WAAWiE,SAAS,CAACC,KAAK,GAAG,CAAC;gBAChC;gBAEA,KAAK,MAAMC,OAAO;oBAAC;oBAAQ;oBAAQ;oBAAS;iBAAQ,CAAE;oBACpDnE,WAAWiE,SAAS,CAACC,KAAK,CAACC,IAAI,GAAG;wBAChCC,SAAS;4BACPC,SAAS;4BACTC,SAAS;gCACPvO,uBACE6N,mBAAmBE,YAAY,CAACC,aAAa,EAC7C7D,aACAF,WAAWtD,GAAG,EACd,OACAtE;6BAEH;wBACH;oBACF;gBACF;YACF;QACF;QAEA,OAAO4H;IACT;IAEA,eAAeC,oBACbD,UAA8B,EAC9BE,WAAmB;YAYfqE,4CAAAA;QAVJ,mDAAmD;QACnD,IAAIA,yBAAyBZ,kBAAkB3D,YAAYE;QAE3DqE,uBAAuBC,eAAe,GACpC,OAAMxE,WAAWwE,eAAe,oBAA1BxE,WAAWwE,eAAe,MAA1BxE;QAER,iFAAiF;QACjFuE,uBAAuBE,aAAa,GAAG,CAAC;QACxCF,uBAAuBG,OAAO,GAAG1E,WAAW0E,OAAO,IAAI,CAAC;QAExD,KAAIH,uCAAAA,uBAAuBT,YAAY,sBAAnCS,6CAAAA,qCAAqCI,KAAK,qBAA1CJ,2CAA4CL,KAAK,EAAE;gBAEnDK;YADFK,sCACEL,oCAAAA,uBAAuBN,SAAS,qBAAhCM,kCAAkCL,KAAK;QAE3C;QAEAK,uBAAuBM,iBAAiB,GACtCN,uBAAuBM,iBAAiB,GACpCzH,OAAO0H,WAAW,CAChB1H,OAAOO,OAAO,CAAM4G,uBAAuBM,iBAAiB,EAAExJ,GAAG,CAC/D,CAAC,CAAC0J,KAAKtI,OAAO,GAAK;gBACjBsI;gBACA;oBACE,GAAGtI,MAAM;oBACTuI,WACE,OAAOvI,OAAOuI,SAAS,KAAK,WACxBvI,OAAOuI,SAAS,GAChB5H,OAAOO,OAAO,CAAClB,OAAOuI,SAAS,EAAE3J,GAAG,CAAC,CAAC,CAAC8I,KAAKtG,MAAM,GAAK;4BACrDsG;4BACAtG;yBACD;gBACT;aACD,KAGLzF;QAEN,2EAA2E;QAC3E,IAAImM,uBAAuBU,MAAM,CAACC,UAAU,EAAE;YAC5CX,uBAAuBU,MAAM,GAAG;gBAC9B,GAAGjF,WAAWiF,MAAM;gBACpBC,YACE,OAAOhQ,KAAKiQ,QAAQ,CAACjF,aAAaF,WAAWiF,MAAM,CAACC,UAAU;YAClE;QACF;QAEA,OAAO9E,KAAKC,SAAS,CAACkE,wBAAwB,MAAM;IACtD;IAEA,SAASK,qCACPQ,cAAiE;QAEjE,KAAK,MAAM,CAACC,MAAMC,KAAK,IAAIlI,OAAOO,OAAO,CAACyH,gBAAiB;YACzD,IAAI1K,MAAMC,OAAO,CAAC2K,OAAO;gBACvBC,iBAAiBD,MAAMD;YACzB,OAAO;gBACLG,gBAAgBF,MAAMD;YACxB;QACF;QAEA,SAASG,gBAAgBF,IAA6B,EAAED,IAAY;YAClE,IAAI,CAACC,MAAM;YACX,IAAI,aAAaA,MAAM;gBACrBC,iBAAiB,AAACD,KAAwChB,OAAO,EAAEe;YACrE,OAAO;gBACL,IAAK,MAAMlB,OAAOmB,KAAM;oBACtB,MAAMG,QAAQH,IAAI,CAACnB,IAAI;oBACvB,IAAI,OAAOsB,UAAU,YAAYA,OAAO;wBACtCD,gBAAgBC,OAAOJ;oBACzB;gBACF;YACF;QACF;QAEA,SAASE,iBACPG,WAAkC,EAClCL,IAAY;YAEZ,KAAK,MAAMM,cAAcD,YAAa;gBACpC,IACE,OAAOC,eAAe,YACtB,CAAC9P,kBAAkB8P,YAAYvF,KAAKwF,KAAK,CAACxF,KAAKC,SAAS,CAACsF,eACzD;oBACA,MAAM,qBAEL,CAFK,IAAI5J,MACR,CAAC,OAAO,EAAE4J,WAAWE,MAAM,CAAC,YAAY,EAAER,KAAK,yGAAyG,CAAC,GADrJ,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;IACF;IAEA,SAASpE,gCACPK,WAA6C;QAE7C,MAAMwE,SAAS,IAAIC;QACnB,KAAK,MAAM,EAAEC,QAAQ,EAAE,GAAGC,aAAa,IAAI3E,YAAYwE,MAAM,CAAE;YAC7D,IAAII;YACJ,MAAMC,YAAYF,YAAYG,IAAI;YAClC,OAAQD;gBACN,KAAK;oBACHD,QAAQ;wBACNE,MAAM;wBACNC,cAAc,IAAIvD,aAAamD,YAAYI,YAAY;wBACvDC,cAAc,IAAIxD,aAAamD,YAAYK,YAAY;oBACzD;oBACA;gBACF,KAAK;oBACHJ,QAAQ;wBACNE,MAAM;wBACNG,UAAU,IAAIzD,aAAamD,YAAYM,QAAQ;oBACjD;oBACA;gBACF,KAAK;oBACHL,QAAQ;wBACNE,MAAM;wBACNI,OAAOP,YAAYO,KAAK,CAACnL,GAAG,CAAC,CAACoL,OAAU,CAAA;gCACtCC,cAAcD,KAAKC,YAAY;gCAC/BL,cAAc,IAAIvD,aAAa2D,KAAKJ,YAAY;gCAChDM,aAAa,IAAI7D,aAAa2D,KAAKE,WAAW;4BAChD,CAAA;oBACF;oBACA;gBACF,KAAK;oBACHT,QAAQ;wBACNE,MAAM;wBACNM,cAAcT,YAAYS,YAAY;wBACtCH,UAAU,IAAIzD,aAAamD,YAAYM,QAAQ;oBACjD;oBACA;gBACF,KAAK;oBACHL,QAAQ;wBACNE,MAAM;oBACR;oBACA;gBACF;oBACE,MAAMQ,mBAA0BT;oBAChC/H,UACE6H,aACA,IAAM,CAAC,oBAAoB,EAAEW,kBAAkB;YAErD;YACAd,OAAOe,GAAG,CAACb,UAAUE;QACvB;QACA,MAAMY,6BAA6B,CAACC,aAAgC,CAAA;gBAClER,UAAU,IAAIzD,aAAaiE,WAAWR,QAAQ;gBAC9CS,SAASD,WAAWC,OAAO;gBAC3BC,SAASF,WAAWE,OAAO;YAC7B,CAAA;QACA,MAAMF,aAAazF,YAAYyF,UAAU,GACrCD,2BAA2BxF,YAAYyF,UAAU,IACjD3O;QACJ,MAAM8O,uCAAuC,CAC3CC,kBACI,CAAA;gBACJC,QAAQ,IAAItE,aAAaqE,gBAAgBC,MAAM;gBAC/CnK,MAAM,IAAI6F,aAAaqE,gBAAgBlK,IAAI;YAC7C,CAAA;QACA,MAAMkK,kBAAkB7F,YAAY6F,eAAe,GAC/CD,qCAAqC5F,YAAY6F,eAAe,IAChE/O;QAEJ,OAAO;YACL0N;YACAiB;YACAI;YACAE,uBAAuB,IAAIvE,aACzBxB,YAAY+F,qBAAqB;YAEnCC,kBAAkB,IAAIxE,aAAaxB,YAAYgG,gBAAgB;YAC/DC,oBAAoB,IAAIzE,aAAaxB,YAAYiG,kBAAkB;YACnEC,QAAQlG,YAAYkG,MAAM;YAC1BC,aAAanG,YAAYmG,WAAW;QACtC;IACF;IAEA,OAAO,eAAeC,cACpB3H,OAAuB,EACvB4H,kBAAkB;QAElB,OAAO,IAAIpH,YACT,MAAMvC,QAAQ4J,UAAU,CACtB,MAAM9H,sBAAsBC,UAC5B4H,sBAAsB,CAAC;IAG7B;AACF;AAEA,eAAelM,SAASoM,aAAa,EAAE;IACrC,IAAIvP,cAAc;QAChB,OAAOA;IACT;IAEA,IAAIqB,WAAW,EAAE;IACjB,KAAK,IAAImO,OAAO;QAAC;QAAyB;KAAqB,CAAE;QAC/D,IAAI;YACF,IAAIC,UAAUD;YAEd,IAAID,YAAY;gBACd,yDAAyD;gBACzDE,UAAU7S,KAAKgG,IAAI,CAAC2M,YAAYC,KAAK;YACvC;YACA,IAAItM,WAA4B,MAAM,MAAM,CAC1CrG,cAAc4S,SAASC,QAAQ;YAEjC,IAAIF,QAAQ,sBAAsB;gBAChCtM,WAAW,MAAMA,SAASyM,OAAO;YACnC;YACA1R,QAAQ;YAER,mEAAmE;YACnE,yCAAyC;YACzC+B,eAAe;gBACb4P,KAAK;oBACHC,WAAW;wBACTnD,WAAW,SAAUoD,QAAa;4BAChC,MAAM,qBAEL,CAFK,IAAIrM,MACR,qEADI,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBACAsM,oBAAoB,SAAUD,QAAa;4BACzC,MAAM,qBAEL,CAFK,IAAIrM,MACR,8EADI,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;oBACF;gBACF;gBACAuM,QAAQ;gBACRtD,WAAUuD,GAAW,EAAExI,OAAY;oBACjC,oHAAoH;oBACpH,OAAOvE,CAAAA,4BAAAA,SAAUwJ,SAAS,IACtBxJ,SAASwJ,SAAS,CAACuD,IAAIP,QAAQ,IAAIjI,WACnC5G,QAAQC,OAAO,CAACoC,SAASgN,aAAa,CAACD,IAAIP,QAAQ,IAAIjI;gBAC7D;gBACAyI,eAAcD,GAAW,EAAExI,OAAY;oBACrC,OAAOvE,SAASgN,aAAa,CAACD,IAAIP,QAAQ,IAAIjI;gBAChD;gBACA0I,QAAOF,GAAW,EAAExI,OAAY;oBAC9B,OAAOvE,CAAAA,4BAAAA,SAAUiN,MAAM,IACnBjN,SAASiN,MAAM,CAACF,IAAIP,QAAQ,IAAIjI,WAChC5G,QAAQC,OAAO,CAACoC,SAASkN,UAAU,CAACH,IAAIP,QAAQ,IAAIjI;gBAC1D;gBACA2I,YAAWH,GAAW,EAAExI,OAAY;oBAClC,OAAOvE,SAASkN,UAAU,CAACH,IAAIP,QAAQ,IAAIjI;gBAC7C;gBACA6F,OAAM2C,GAAW,EAAExI,OAAY;oBAC7B,OAAOvE,CAAAA,4BAAAA,SAAUoK,KAAK,IAClBpK,SAASoK,KAAK,CAAC2C,IAAIP,QAAQ,IAAIjI,WAC/B5G,QAAQC,OAAO,CAACoC,SAASmN,SAAS,CAACJ,IAAIP,QAAQ,IAAIjI;gBACzD;gBACA6I;oBACE,OAAOxQ;gBACT;gBACAuM,OAAO;oBACL+C,eAAe,SACbU,QAAwB,EACxBS,mBAAoD;wBAEpD,MAAM,qBAEL,CAFK,IAAI9M,MACR,iEADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBACA+M,2BAA2B,SAAUC,cAAsB;wBACzD,MAAM,qBAEL,CAFK,IAAIhN,MACR,6EADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBACAiN,KAAK;oBACHC,SAAQV,GAAW,EAAExI,OAAY;wBAC/B,OAAOvE,SAAS0N,UAAU,CAACX,KAAKY,cAAcpJ;oBAChD;oBACAqJ,aAAYb,GAAW,EAAExI,OAAY;wBACnC,OAAOvE,SAAS6N,cAAc,CAACd,KAAKY,cAAcpJ;oBACpD;gBACF;gBACAgE,eAAe;oBACbuF,yBAAwBC,SAAiB;wBACvC,OAAOpQ,QAAQC,OAAO,CAAC;oBACzB;gBACF;YACF;YACA,OAAOd;QACT,EAAE,OAAOmH,GAAQ;YACf,8DAA8D;YAC9D,IAAIoI,YAAY;gBACd,IAAIpI,CAAAA,qBAAAA,EAAG+J,IAAI,MAAK,wBAAwB;oBACtC7P,SAAS4B,IAAI,CAAC,CAAC,kBAAkB,EAAEuM,IAAI,0BAA0B,CAAC;gBACpE,OAAO;oBACLnO,SAAS4B,IAAI,CACX,CAAC,kBAAkB,EAAEuM,IAAI,yBAAyB,EAAErI,EAAEgK,OAAO,IAAIhK,GAAG;gBAExE;YACF;QACF;IACF;IAEA,MAAM9F;AACR;AAEA,SAASa,WAAWqN,UAAmB;IACrC,IAAIxP,gBAAgB;QAClB,OAAOA;IACT;IAEA,MAAMqR,iBAA8B,CAAC,CAAC5R,uCAClCsD,QAAQtD,wCACR;IACJ,IAAI0D,WAAwBkO;IAC5B,IAAI/P,WAAkB,EAAE;IAExB,MAAMgQ,uBAAuBzT,QAAQC,GAAG,CAACwT,oBAAoB;IAC7D,KAAK,MAAMtS,UAAUI,QAAS;QAC5B,IAAIkS,sBAAsB;YACxB,IAAI;gBACF,2GAA2G;gBAC3GnO,WAAWJ,QACT,GAAGuO,qBAAqB,UAAU,EAAEtS,OAAOiE,eAAe,CAAC,KAAK,CAAC;gBAEnE/E,QACE;gBAEF;YACF,EAAE,OAAOkJ,GAAG,CAAC;QACf,OAAO;YACL,IAAI;gBACFjE,WAAWJ,QACT,CAAC,0BAA0B,EAAE/D,OAAOiE,eAAe,CAAC,KAAK,CAAC;gBAE5D/E,QAAQ;gBACR;YACF,EAAE,OAAOkJ,GAAG,CAAC;QACf;IACF;IAEA,IAAI,CAACjE,UAAU;QACb,KAAK,MAAMnE,UAAUI,QAAS;YAC5B,IAAIqQ,MAAMD,aACN3S,KAAKgG,IAAI,CACP2M,YACA,CAAC,UAAU,EAAExQ,OAAOiE,eAAe,EAAE,EACrC,CAAC,SAAS,EAAEjE,OAAOiE,eAAe,CAAC,KAAK,CAAC,IAE3C,CAAC,UAAU,EAAEjE,OAAOiE,eAAe,EAAE;YACzC,IAAI;gBACFE,WAAWJ,QAAQ0M;gBACnB,IAAI,CAACD,YAAY;oBACf9P,qBAAqBqD,QAAQ,GAAG0M,IAAI,aAAa,CAAC;gBACpD;gBACA;YACF,EAAE,OAAOrI,GAAQ;gBACf,IAAIA,CAAAA,qBAAAA,EAAG+J,IAAI,MAAK,oBAAoB;oBAClC7P,SAAS4B,IAAI,CAAC,CAAC,kBAAkB,EAAEuM,IAAI,0BAA0B,CAAC;gBACpE,OAAO;oBACLnO,SAAS4B,IAAI,CACX,CAAC,kBAAkB,EAAEuM,IAAI,yBAAyB,EAAErI,EAAEgK,OAAO,IAAIhK,GAAG;gBAExE;gBACAtH,kCAAkCsH,CAAAA,qBAAAA,EAAG+J,IAAI,KAAI;YAC/C;QACF;IACF;IAEA,IAAIhO,UAAU;QACZnD,iBAAiB;YACfiQ,QAAQ;YACRtD,WAAUuD,GAAW,EAAExI,OAAY;oBAO7BA;gBANJ,MAAM6J,WACJ,OAAOrB,QAAQ,eACf,OAAOA,QAAQ,YACf,CAACsB,OAAOC,QAAQ,CAACvB;gBACnBxI,UAAUA,WAAW,CAAC;gBAEtB,IAAIA,4BAAAA,eAAAA,QAASgK,GAAG,qBAAZhK,aAAciK,MAAM,EAAE;oBACxBjK,QAAQgK,GAAG,CAACC,MAAM,CAACC,MAAM,GAAGlK,QAAQgK,GAAG,CAACC,MAAM,CAACC,MAAM,IAAI;gBAC3D;gBAEA,OAAOzO,SAASwJ,SAAS,CACvB4E,WAAWxJ,KAAKC,SAAS,CAACkI,OAAOA,KACjCqB,UACAM,SAASnK;YAEb;YAEAyI,eAAcD,GAAW,EAAExI,OAAY;oBAajCA;gBAZJ,IAAI,OAAOwI,QAAQ,aAAa;oBAC9B,MAAM,qBAEL,CAFK,IAAIxM,MACR,qEADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,OAAO,IAAI8N,OAAOC,QAAQ,CAACvB,MAAM;oBAC/B,MAAM,qBAEL,CAFK,IAAIxM,MACR,qEADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM6N,WAAW,OAAOrB,QAAQ;gBAChCxI,UAAUA,WAAW,CAAC;gBAEtB,IAAIA,4BAAAA,eAAAA,QAASgK,GAAG,qBAAZhK,aAAciK,MAAM,EAAE;oBACxBjK,QAAQgK,GAAG,CAACC,MAAM,CAACC,MAAM,GAAGlK,QAAQgK,GAAG,CAACC,MAAM,CAACC,MAAM,IAAI;gBAC3D;gBAEA,OAAOzO,SAASgN,aAAa,CAC3BoB,WAAWxJ,KAAKC,SAAS,CAACkI,OAAOA,KACjCqB,UACAM,SAASnK;YAEb;YAEA0I,QAAOF,GAAW,EAAExI,OAAY;gBAC9B,OAAOvE,SAASiN,MAAM,CAACyB,SAAS3B,MAAM2B,SAASnK,WAAW,CAAC;YAC7D;YAEA2I,YAAWH,GAAW,EAAExI,OAAY;gBAClC,OAAOvE,SAASkN,UAAU,CAACwB,SAAS3B,MAAM2B,SAASnK,WAAW,CAAC;YACjE;YAEA6F,OAAM2C,GAAW,EAAExI,OAAY;gBAC7B,OAAOvE,SAASoK,KAAK,CAAC2C,KAAK2B,SAASnK,WAAW,CAAC;YAClD;YAEA6I,iBAAiBpN,SAASoN,eAAe;YACzCuB,2BAA2B3O,SAAS2O,yBAAyB;YAC7DC,yBAAyB5O,SAAS4O,uBAAuB;YACzDzF,OAAO;gBACL+C,eAAe3J,aAAa2L,kBAAkBlO,UAAU;gBACxDsN,2BAA0BuB,aAAa;oBACrC9U,IAAIsC,IAAI,CACN;oBAEA6R,CAAAA,kBAAkBlO,QAAO,EAAGsN,yBAAyB,CAACuB;gBAC1D;YACF;YACArB,KAAK;gBACHC,SAAQV,GAAW,EAAExI,OAAY;oBAC/B,OAAOvE,SAAS0N,UAAU,CAACX,KAAK2B,SAASf,cAAcpJ;gBACzD;gBACAqJ,aAAYb,GAAW,EAAExI,OAAY;oBACnCvE,SAAS6N,cAAc,CAACd,KAAK2B,SAASf,cAAcpJ;gBACtD;YACF;YACAmI,KAAK;gBACHC,WAAW;oBACTnD,WAAUsF,gBAAqB;wBAC7B,OAAO9O,SAAS+O,qBAAqB,CAACD;oBACxC;oBACAjC,oBAAmBmC,oBAAyB;wBAC1C,OAAOhP,SAASiP,mCAAmC,CACjDD;oBAEJ;gBACF;YACF;YACAzG,eAAe;gBACbuF,yBAAyB,CAACoB;oBACxB,OAAOlP,SAAS8N,uBAAuB,CAACoB;gBAC1C;YACF;QACF;QACA,OAAOrS;IACT;IAEA,MAAMsB;AACR;AAEA,2DAA2D;AAC3D,0CAA0C;AAC1C,SAASwP,cAAcpJ,UAAe,CAAC,CAAC;IACtC,OAAO;QACL,GAAGA,OAAO;QACV4K,aAAa5K,QAAQ4K,WAAW,IAAI;QACpCC,KAAK7K,QAAQ6K,GAAG,IAAI;QACpBC,SAAS9K,QAAQ8K,OAAO,IAAI;IAC9B;AACF;AAEA,SAASX,SAASY,CAAM;IACtB,OAAOjB,OAAOkB,IAAI,CAAC3K,KAAKC,SAAS,CAACyK;AACpC;AAEA,OAAO,eAAexC;IACpB,IAAI9M,WAAW,MAAM5C;IACrB,OAAO4C,SAAS8M,MAAM;AACxB;AAEA,OAAO,eAAetD,UAAUuD,GAAW,EAAExI,OAAa;IACxD,IAAIvE,WAAW,MAAM5C;IACrB,OAAO4C,SAASwJ,SAAS,CAACuD,KAAKxI;AACjC;AAEA,OAAO,SAASyI,cAAcD,GAAW,EAAExI,OAAa;IACtD,IAAIvE,WAAWM;IACf,OAAON,SAASgN,aAAa,CAACD,KAAKxI;AACrC;AAEA,OAAO,eAAe0I,OACpBF,GAAW,EACXxI,OAAY;IAEZ,IAAIvE,WAAW,MAAM5C;IACrB,OAAO4C,SAASiN,MAAM,CAACF,KAAKxI;AAC9B;AAEA,OAAO,eAAeuJ,wBACpBoB,QAAgB;IAEhB,IAAIlP,WAAW,MAAM5C;IACrB,OAAO4C,SAASuI,aAAa,CAACuF,uBAAuB,CAACoB;AACxD;AAEA,OAAO,eAAe9E,MAAM2C,GAAW,EAAExI,OAAY;IACnD,IAAIvE,WAAW,MAAM5C;IACrB,IAAIoS,gBAAgBxV,iBAAiBuK;IACrC,OAAOvE,SACJoK,KAAK,CAAC2C,KAAKyC,eACX7O,IAAI,CAAC,CAAC8O,SAAgB7K,KAAKwF,KAAK,CAACqF;AACtC;AAEA,OAAO,SAASC;QASJ1P;IARV,IAAIA;IACJ,IAAI;QACFA,WAAWhB;IACb,EAAE,OAAOiF,GAAG;IACV,sEAAsE;IACxE;IAEA,OAAO;QACL0L,MAAM,EAAE3P,6BAAAA,4BAAAA,SAAUoN,eAAe,qBAAzBpN,+BAAAA;IACV;AACF;AAEA;;;CAGC,GACD,OAAO,SAAS2O,0BAA0BiB,aAAsB;IAC9D,IAAI3S,oBAAoB;QACtB,6CAA6C;QAC7C,IAAI+C,WAAWhB;QACf/B,qBAAqB+C,SAAS2O,yBAAyB,oBAAlC3O,SAAS2O,yBAAyB,MAAlC3O,UAAqC4P;IAC5D;AACF;AAEA,SAASC,KAAK7M,EAAc;IAC1B,IAAI8M,WAAW;IAEf,OAAO;QACL,IAAI,CAACA,UAAU;YACbA,WAAW;YAEX9M;QACF;IACF;AACF;AAEA;;;;;;;;CAQC,GACD,OAAO,MAAM4L,0BAA0BiB,KAAK;IAC1C,IAAI;QACF,IAAI7P,WAAWhB;QACf,IAAI/B,oBAAoB;YACtB+C,SAAS4O,uBAAuB,oBAAhC5O,SAAS4O,uBAAuB,MAAhC5O,UAAmC/C;QACrC;IACF,EAAE,OAAOgH,GAAG;IACV,sEAAsE;IACxE;AACF,GAAE"}