{"version": 3, "sources": ["../../../src/build/webpack-build/index.ts"], "sourcesContent": ["import type { COMPILER_INDEXES } from '../../shared/lib/constants'\nimport * as Log from '../output/log'\nimport { NextBuildContext } from '../build-context'\nimport type { BuildTraceContext } from '../webpack/plugins/next-trace-entrypoints-plugin'\nimport { Worker } from '../../lib/worker'\nimport origDebug from 'next/dist/compiled/debug'\nimport path from 'path'\nimport { exportTraceState, recordTraceEvents } from '../../trace'\nimport {\n  formatNodeOptions,\n  getParsedNodeOptionsWithoutInspect,\n} from '../../server/lib/utils'\nimport { mergeUseCacheTrackers } from '../webpack/plugins/telemetry-plugin/use-cache-tracker-utils'\nimport { durationToString } from '../duration-to-string'\n\nconst debug = origDebug('next:build:webpack-build')\n\nconst ORDERED_COMPILER_NAMES = [\n  'server',\n  'edge-server',\n  'client',\n] as (keyof typeof COMPILER_INDEXES)[]\n\nlet pluginState: Record<any, any> = {}\n\nfunction deepMerge(target: any, source: any) {\n  const result = { ...target, ...source }\n  for (const key of Object.keys(result)) {\n    result[key] = Array.isArray(target[key])\n      ? (target[key] = [...target[key], ...(source[key] || [])])\n      : typeof target[key] == 'object' && typeof source[key] == 'object'\n        ? deepMerge(target[key], source[key])\n        : result[key]\n  }\n  return result\n}\n\nasync function webpackBuildWithWorker(\n  compilerNamesArg: typeof ORDERED_COMPILER_NAMES | null\n) {\n  const compilerNames = compilerNamesArg || ORDERED_COMPILER_NAMES\n  const { nextBuildSpan, ...prunedBuildContext } = NextBuildContext\n\n  prunedBuildContext.pluginState = pluginState\n\n  const combinedResult = {\n    duration: 0,\n    buildTraceContext: {} as BuildTraceContext,\n  }\n\n  const nodeOptions = getParsedNodeOptionsWithoutInspect()\n\n  for (const compilerName of compilerNames) {\n    const worker = new Worker(path.join(__dirname, 'impl.js'), {\n      exposedMethods: ['workerMain'],\n      numWorkers: 1,\n      maxRetries: 0,\n      forkOptions: {\n        env: {\n          ...process.env,\n          NEXT_PRIVATE_BUILD_WORKER: '1',\n          NODE_OPTIONS: formatNodeOptions(nodeOptions),\n        },\n      },\n    }) as Worker & typeof import('./impl')\n\n    const curResult = await worker.workerMain({\n      buildContext: prunedBuildContext,\n      compilerName,\n      traceState: {\n        ...exportTraceState(),\n        defaultParentSpanId: nextBuildSpan?.getId(),\n        shouldSaveTraceEvents: true,\n      },\n    })\n    if (nextBuildSpan && curResult.debugTraceEvents) {\n      recordTraceEvents(curResult.debugTraceEvents)\n    }\n    // destroy worker so it's not sticking around using memory\n    await worker.end()\n\n    // Update plugin state\n    pluginState = deepMerge(pluginState, curResult.pluginState)\n    prunedBuildContext.pluginState = pluginState\n\n    if (curResult.telemetryState) {\n      NextBuildContext.telemetryState = {\n        ...curResult.telemetryState,\n        useCacheTracker: mergeUseCacheTrackers(\n          NextBuildContext.telemetryState?.useCacheTracker,\n          curResult.telemetryState.useCacheTracker\n        ),\n      }\n    }\n\n    combinedResult.duration += curResult.duration\n\n    if (curResult.buildTraceContext?.entriesTrace) {\n      const { entryNameMap } = curResult.buildTraceContext.entriesTrace!\n\n      if (entryNameMap) {\n        combinedResult.buildTraceContext.entriesTrace =\n          curResult.buildTraceContext.entriesTrace\n        combinedResult.buildTraceContext.entriesTrace!.entryNameMap =\n          entryNameMap\n      }\n\n      if (curResult.buildTraceContext?.chunksTrace) {\n        const { entryNameFilesMap } = curResult.buildTraceContext.chunksTrace!\n\n        if (entryNameFilesMap) {\n          combinedResult.buildTraceContext.chunksTrace =\n            curResult.buildTraceContext.chunksTrace!\n\n          combinedResult.buildTraceContext.chunksTrace!.entryNameFilesMap =\n            entryNameFilesMap\n        }\n      }\n    }\n  }\n\n  if (compilerNames.length === 3) {\n    const durationString = durationToString(combinedResult.duration)\n    Log.event(`Compiled successfully in ${durationString}`)\n  }\n\n  return combinedResult\n}\n\nexport async function webpackBuild(\n  withWorker: boolean,\n  compilerNames: typeof ORDERED_COMPILER_NAMES | null\n): ReturnType<typeof webpackBuildWithWorker> {\n  if (withWorker) {\n    debug('using separate compiler workers')\n    return await webpackBuildWithWorker(compilerNames)\n  } else {\n    debug('building all compilers in same process')\n    const webpackBuildImpl = require('./impl').webpackBuildImpl\n    const curResult = await webpackBuildImpl(null, null)\n\n    // Mirror what happens in webpackBuildWithWorker\n    if (curResult.telemetryState) {\n      NextBuildContext.telemetryState = {\n        ...curResult.telemetryState,\n        useCacheTracker: mergeUseCacheTrackers(\n          NextBuildContext.telemetryState?.useCacheTracker,\n          curResult.telemetryState.useCacheTracker\n        ),\n      }\n    }\n\n    return curResult\n  }\n}\n"], "names": ["Log", "NextBuildContext", "Worker", "origDebug", "path", "exportTraceState", "recordTraceEvents", "formatNodeOptions", "getParsedNodeOptionsWithoutInspect", "mergeUseCacheTrackers", "durationToString", "debug", "ORDERED_COMPILER_NAMES", "pluginState", "deepMerge", "target", "source", "result", "key", "Object", "keys", "Array", "isArray", "webpackBuildWithWorker", "compilerNamesArg", "compilerNames", "nextBuildSpan", "prunedBuildContext", "combinedResult", "duration", "buildTraceContext", "nodeOptions", "compilerName", "curR<PERSON>ult", "worker", "join", "__dirname", "exposedMethods", "numWorkers", "maxRetries", "forkOptions", "env", "process", "NEXT_PRIVATE_BUILD_WORKER", "NODE_OPTIONS", "worker<PERSON>ain", "buildContext", "traceState", "defaultParentSpanId", "getId", "shouldSaveTraceEvents", "debugTraceEvents", "end", "telemetryState", "useCacheTracker", "entriesTrace", "entryNameMap", "chunksTrace", "entryNameFilesMap", "length", "durationString", "event", "webpackBuild", "with<PERSON><PERSON>ker", "webpackBuildImpl", "require"], "mappings": "AACA,YAAYA,SAAS,gBAAe;AACpC,SAASC,gBAAgB,QAAQ,mBAAkB;AAEnD,SAASC,MAAM,QAAQ,mBAAkB;AACzC,OAAOC,eAAe,2BAA0B;AAChD,OAAOC,UAAU,OAAM;AACvB,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,cAAa;AACjE,SACEC,iBAAiB,EACjBC,kCAAkC,QAC7B,yBAAwB;AAC/B,SAASC,qBAAqB,QAAQ,8DAA6D;AACnG,SAASC,gBAAgB,QAAQ,wBAAuB;AAExD,MAAMC,QAAQR,UAAU;AAExB,MAAMS,yBAAyB;IAC7B;IACA;IACA;CACD;AAED,IAAIC,cAAgC,CAAC;AAErC,SAASC,UAAUC,MAAW,EAAEC,MAAW;IACzC,MAAMC,SAAS;QAAE,GAAGF,MAAM;QAAE,GAAGC,MAAM;IAAC;IACtC,KAAK,MAAME,OAAOC,OAAOC,IAAI,CAACH,QAAS;QACrCA,MAAM,CAACC,IAAI,GAAGG,MAAMC,OAAO,CAACP,MAAM,CAACG,IAAI,IAClCH,MAAM,CAACG,IAAI,GAAG;eAAIH,MAAM,CAACG,IAAI;eAAMF,MAAM,CAACE,IAAI,IAAI,EAAE;SAAE,GACvD,OAAOH,MAAM,CAACG,IAAI,IAAI,YAAY,OAAOF,MAAM,CAACE,IAAI,IAAI,WACtDJ,UAAUC,MAAM,CAACG,IAAI,EAAEF,MAAM,CAACE,IAAI,IAClCD,MAAM,CAACC,IAAI;IACnB;IACA,OAAOD;AACT;AAEA,eAAeM,uBACbC,gBAAsD;IAEtD,MAAMC,gBAAgBD,oBAAoBZ;IAC1C,MAAM,EAAEc,aAAa,EAAE,GAAGC,oBAAoB,GAAG1B;IAEjD0B,mBAAmBd,WAAW,GAAGA;IAEjC,MAAMe,iBAAiB;QACrBC,UAAU;QACVC,mBAAmB,CAAC;IACtB;IAEA,MAAMC,cAAcvB;IAEpB,KAAK,MAAMwB,gBAAgBP,cAAe;YA6CpCQ;QA5CJ,MAAMC,SAAS,IAAIhC,OAAOE,KAAK+B,IAAI,CAACC,WAAW,YAAY;YACzDC,gBAAgB;gBAAC;aAAa;YAC9BC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACdE,2BAA2B;oBAC3BC,cAAcrC,kBAAkBwB;gBAClC;YACF;QACF;QAEA,MAAME,YAAY,MAAMC,OAAOW,UAAU,CAAC;YACxCC,cAAcnB;YACdK;YACAe,YAAY;gBACV,GAAG1C,kBAAkB;gBACrB2C,mBAAmB,EAAEtB,iCAAAA,cAAeuB,KAAK;gBACzCC,uBAAuB;YACzB;QACF;QACA,IAAIxB,iBAAiBO,UAAUkB,gBAAgB,EAAE;YAC/C7C,kBAAkB2B,UAAUkB,gBAAgB;QAC9C;QACA,0DAA0D;QAC1D,MAAMjB,OAAOkB,GAAG;QAEhB,sBAAsB;QACtBvC,cAAcC,UAAUD,aAAaoB,UAAUpB,WAAW;QAC1Dc,mBAAmBd,WAAW,GAAGA;QAEjC,IAAIoB,UAAUoB,cAAc,EAAE;gBAIxBpD;YAHJA,iBAAiBoD,cAAc,GAAG;gBAChC,GAAGpB,UAAUoB,cAAc;gBAC3BC,iBAAiB7C,uBACfR,mCAAAA,iBAAiBoD,cAAc,qBAA/BpD,iCAAiCqD,eAAe,EAChDrB,UAAUoB,cAAc,CAACC,eAAe;YAE5C;QACF;QAEA1B,eAAeC,QAAQ,IAAII,UAAUJ,QAAQ;QAE7C,KAAII,+BAAAA,UAAUH,iBAAiB,qBAA3BG,6BAA6BsB,YAAY,EAAE;gBAUzCtB;YATJ,MAAM,EAAEuB,YAAY,EAAE,GAAGvB,UAAUH,iBAAiB,CAACyB,YAAY;YAEjE,IAAIC,cAAc;gBAChB5B,eAAeE,iBAAiB,CAACyB,YAAY,GAC3CtB,UAAUH,iBAAiB,CAACyB,YAAY;gBAC1C3B,eAAeE,iBAAiB,CAACyB,YAAY,CAAEC,YAAY,GACzDA;YACJ;YAEA,KAAIvB,gCAAAA,UAAUH,iBAAiB,qBAA3BG,8BAA6BwB,WAAW,EAAE;gBAC5C,MAAM,EAAEC,iBAAiB,EAAE,GAAGzB,UAAUH,iBAAiB,CAAC2B,WAAW;gBAErE,IAAIC,mBAAmB;oBACrB9B,eAAeE,iBAAiB,CAAC2B,WAAW,GAC1CxB,UAAUH,iBAAiB,CAAC2B,WAAW;oBAEzC7B,eAAeE,iBAAiB,CAAC2B,WAAW,CAAEC,iBAAiB,GAC7DA;gBACJ;YACF;QACF;IACF;IAEA,IAAIjC,cAAckC,MAAM,KAAK,GAAG;QAC9B,MAAMC,iBAAiBlD,iBAAiBkB,eAAeC,QAAQ;QAC/D7B,IAAI6D,KAAK,CAAC,CAAC,yBAAyB,EAAED,gBAAgB;IACxD;IAEA,OAAOhC;AACT;AAEA,OAAO,eAAekC,aACpBC,UAAmB,EACnBtC,aAAmD;IAEnD,IAAIsC,YAAY;QACdpD,MAAM;QACN,OAAO,MAAMY,uBAAuBE;IACtC,OAAO;QACLd,MAAM;QACN,MAAMqD,mBAAmBC,QAAQ,UAAUD,gBAAgB;QAC3D,MAAM/B,YAAY,MAAM+B,iBAAiB,MAAM;QAE/C,gDAAgD;QAChD,IAAI/B,UAAUoB,cAAc,EAAE;gBAIxBpD;YAHJA,iBAAiBoD,cAAc,GAAG;gBAChC,GAAGpB,UAAUoB,cAAc;gBAC3BC,iBAAiB7C,uBACfR,mCAAAA,iBAAiBoD,cAAc,qBAA/BpD,iCAAiCqD,eAAe,EAChDrB,UAAUoB,cAAc,CAACC,eAAe;YAE5C;QACF;QAEA,OAAOrB;IACT;AACF"}