{"version": 3, "sources": ["../../../../src/build/segment-config/middleware/middleware-config.ts"], "sourcesContent": ["import picomatch from 'next/dist/compiled/picomatch'\nimport { z } from 'next/dist/compiled/zod'\nimport { tryToParsePath } from '../../../lib/try-to-parse-path'\nimport type { RouteHas } from '../../../lib/load-custom-routes'\n\nconst RouteHasSchema = z.discriminatedUnion('type', [\n  z\n    .object({\n      type: z.enum(['header', 'query', 'cookie']),\n      key: z.string({\n        required_error: 'key is required when type is header, query or cookie',\n      }),\n      value: z\n        .string({\n          invalid_type_error: 'value must be a string',\n        })\n        .optional(),\n    })\n    .strict(),\n  z\n    .object({\n      type: z.literal('host'),\n      value: z.string({\n        required_error: 'host must have a value',\n      }),\n    })\n    .strict(),\n])\n\n/**\n * @internal - required to exclude zod types from the build\n */\nexport const SourceSchema = z\n  .string({\n    required_error: 'source is required',\n  })\n  .max(4096, 'exceeds max built length of 4096 for route')\n  .superRefine((val, ctx) => {\n    if (!val.startsWith('/')) {\n      return ctx.addIssue({\n        code: z.ZodIssueCode.custom,\n        message: `source must start with /`,\n      })\n    }\n\n    const { error, regexStr } = tryToParsePath(val)\n\n    if (error || !regexStr) {\n      ctx.addIssue({\n        code: z.ZodIssueCode.custom,\n        message: `Invalid source '${val}': ${error.message}`,\n      })\n    }\n  })\n\nconst MiddlewareMatcherInputSchema = z\n  .object({\n    locale: z.union([z.literal(false), z.undefined()]).optional(),\n    has: z.array(RouteHasSchema).optional(),\n    missing: z.array(RouteHasSchema).optional(),\n    source: SourceSchema,\n  })\n  .strict()\n\nconst MiddlewareConfigMatcherInputSchema = z.union([\n  SourceSchema,\n  z.array(\n    z.union([SourceSchema, MiddlewareMatcherInputSchema], {\n      invalid_type_error: 'must be an array of strings or middleware matchers',\n    })\n  ),\n])\n\n/**\n * @internal - required to exclude zod types from the build\n */\nexport type MiddlewareConfigMatcherInput = z.infer<\n  typeof MiddlewareConfigMatcherInputSchema\n>\n\nconst GlobSchema = z.string().superRefine((val, ctx) => {\n  try {\n    picomatch(val)\n  } catch (err: any) {\n    ctx.addIssue({\n      code: z.ZodIssueCode.custom,\n      message: `Invalid glob pattern '${val}': ${err.message}`,\n    })\n  }\n})\n\n/**\n * @internal - required to exclude zod types from the build\n */\nexport const MiddlewareConfigInputSchema = z.object({\n  /**\n   * The matcher for the middleware.\n   */\n  matcher: MiddlewareConfigMatcherInputSchema.optional(),\n\n  /**\n   * The regions that the middleware should run in.\n   */\n  regions: z.union([z.string(), z.array(z.string())]).optional(),\n\n  /**\n   * A glob, or an array of globs, ignoring dynamic code evaluation for specific\n   * files. The globs are relative to your application root folder.\n   */\n  unstable_allowDynamic: z.union([GlobSchema, z.array(GlobSchema)]).optional(),\n})\n\nexport type MiddlewareConfigInput = {\n  /**\n   * The matcher for the middleware.\n   */\n  matcher?:\n    | string\n    | Array<\n        | {\n            locale?: false\n            has?: RouteHas[]\n            missing?: RouteHas[]\n            source: string\n          }\n        | string\n      >\n\n  /**\n   * The regions that the middleware should run in.\n   */\n  regions?: string | string[]\n\n  /**\n   * A glob, or an array of globs, ignoring dynamic code evaluation for specific\n   * files. The globs are relative to your application root folder.\n   */\n  unstable_allowDynamic?: string | string[]\n}\n\n/**\n * The keys of the configuration for a middleware.\n *\n * @internal - required to exclude zod types from the build\n */\nexport const MiddlewareConfigInputSchemaKeys =\n  MiddlewareConfigInputSchema.keyof().options\n"], "names": ["picomatch", "z", "tryToParsePath", "RouteHasSchema", "discriminatedUnion", "object", "type", "enum", "key", "string", "required_error", "value", "invalid_type_error", "optional", "strict", "literal", "SourceSchema", "max", "superRefine", "val", "ctx", "startsWith", "addIssue", "code", "ZodIssueCode", "custom", "message", "error", "regexStr", "MiddlewareMatcherInputSchema", "locale", "union", "undefined", "has", "array", "missing", "source", "MiddlewareConfigMatcherInputSchema", "GlobSchema", "err", "MiddlewareConfigInputSchema", "matcher", "regions", "unstable_allowDynamic", "MiddlewareConfigInputSchemaKeys", "keyof", "options"], "mappings": "AAAA,OAAOA,eAAe,+BAA8B;AACpD,SAASC,CAAC,QAAQ,yBAAwB;AAC1C,SAASC,cAAc,QAAQ,iCAAgC;AAG/D,MAAMC,iBAAiBF,EAAEG,kBAAkB,CAAC,QAAQ;IAClDH,EACGI,MAAM,CAAC;QACNC,MAAML,EAAEM,IAAI,CAAC;YAAC;YAAU;YAAS;SAAS;QAC1CC,KAAKP,EAAEQ,MAAM,CAAC;YACZC,gBAAgB;QAClB;QACAC,OAAOV,EACJQ,MAAM,CAAC;YACNG,oBAAoB;QACtB,GACCC,QAAQ;IACb,GACCC,MAAM;IACTb,EACGI,MAAM,CAAC;QACNC,MAAML,EAAEc,OAAO,CAAC;QAChBJ,OAAOV,EAAEQ,MAAM,CAAC;YACdC,gBAAgB;QAClB;IACF,GACCI,MAAM;CACV;AAED;;CAEC,GACD,OAAO,MAAME,eAAef,EACzBQ,MAAM,CAAC;IACNC,gBAAgB;AAClB,GACCO,GAAG,CAAC,MAAM,8CACVC,WAAW,CAAC,CAACC,KAAKC;IACjB,IAAI,CAACD,IAAIE,UAAU,CAAC,MAAM;QACxB,OAAOD,IAAIE,QAAQ,CAAC;YAClBC,MAAMtB,EAAEuB,YAAY,CAACC,MAAM;YAC3BC,SAAS,CAAC,wBAAwB,CAAC;QACrC;IACF;IAEA,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAE,GAAG1B,eAAeiB;IAE3C,IAAIQ,SAAS,CAACC,UAAU;QACtBR,IAAIE,QAAQ,CAAC;YACXC,MAAMtB,EAAEuB,YAAY,CAACC,MAAM;YAC3BC,SAAS,CAAC,gBAAgB,EAAEP,IAAI,GAAG,EAAEQ,MAAMD,OAAO,EAAE;QACtD;IACF;AACF,GAAE;AAEJ,MAAMG,+BAA+B5B,EAClCI,MAAM,CAAC;IACNyB,QAAQ7B,EAAE8B,KAAK,CAAC;QAAC9B,EAAEc,OAAO,CAAC;QAAQd,EAAE+B,SAAS;KAAG,EAAEnB,QAAQ;IAC3DoB,KAAKhC,EAAEiC,KAAK,CAAC/B,gBAAgBU,QAAQ;IACrCsB,SAASlC,EAAEiC,KAAK,CAAC/B,gBAAgBU,QAAQ;IACzCuB,QAAQpB;AACV,GACCF,MAAM;AAET,MAAMuB,qCAAqCpC,EAAE8B,KAAK,CAAC;IACjDf;IACAf,EAAEiC,KAAK,CACLjC,EAAE8B,KAAK,CAAC;QAACf;QAAca;KAA6B,EAAE;QACpDjB,oBAAoB;IACtB;CAEH;AASD,MAAM0B,aAAarC,EAAEQ,MAAM,GAAGS,WAAW,CAAC,CAACC,KAAKC;IAC9C,IAAI;QACFpB,UAAUmB;IACZ,EAAE,OAAOoB,KAAU;QACjBnB,IAAIE,QAAQ,CAAC;YACXC,MAAMtB,EAAEuB,YAAY,CAACC,MAAM;YAC3BC,SAAS,CAAC,sBAAsB,EAAEP,IAAI,GAAG,EAAEoB,IAAIb,OAAO,EAAE;QAC1D;IACF;AACF;AAEA;;CAEC,GACD,OAAO,MAAMc,8BAA8BvC,EAAEI,MAAM,CAAC;IAClD;;GAEC,GACDoC,SAASJ,mCAAmCxB,QAAQ;IAEpD;;GAEC,GACD6B,SAASzC,EAAE8B,KAAK,CAAC;QAAC9B,EAAEQ,MAAM;QAAIR,EAAEiC,KAAK,CAACjC,EAAEQ,MAAM;KAAI,EAAEI,QAAQ;IAE5D;;;GAGC,GACD8B,uBAAuB1C,EAAE8B,KAAK,CAAC;QAACO;QAAYrC,EAAEiC,KAAK,CAACI;KAAY,EAAEzB,QAAQ;AAC5E,GAAE;AA8BF;;;;CAIC,GACD,OAAO,MAAM+B,kCACXJ,4BAA4BK,KAAK,GAAGC,OAAO,CAAA"}