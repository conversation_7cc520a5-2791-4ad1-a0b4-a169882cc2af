{"version": 3, "sources": ["../../../../src/build/segment-config/pages/pages-segment-config.ts"], "sourcesContent": ["import { z } from 'next/dist/compiled/zod'\nimport { formatZodError } from '../../../shared/lib/zod'\n\n/**\n * The schema for the page segment config.\n */\nconst PagesSegmentConfigSchema = z.object({\n  /**\n   * The runtime to use for the page.\n   */\n  runtime: z.enum(['edge', 'experimental-edge', 'nodejs']).optional(),\n\n  /**\n   * The maximum duration for the page render.\n   */\n  maxDuration: z.number().optional(),\n\n  /**\n   * The exported config object for the page.\n   */\n  config: z\n    .object({\n      /**\n       * Enables AMP for the page.\n       */\n      amp: z.union([z.boolean(), z.literal('hybrid')]).optional(),\n\n      /**\n       * The runtime to use for the page.\n       */\n      runtime: z.enum(['edge', 'experimental-edge', 'nodejs']).optional(),\n\n      /**\n       * The maximum duration for the page render.\n       */\n      maxDuration: z.number().optional(),\n    })\n    .optional(),\n})\n\n/**\n * Parse the page segment config.\n * @param data - The data to parse.\n * @param route - The route of the page.\n * @returns The parsed page segment config.\n */\nexport function parsePagesSegmentConfig(\n  data: unknown,\n  route: string\n): PagesSegmentConfig {\n  const parsed = PagesSegmentConfigSchema.safeParse(data, {})\n  if (!parsed.success) {\n    throw formatZodError(\n      `Invalid segment configuration options detected for \"${route}\". Read more at https://nextjs.org/docs/messages/invalid-page-config`,\n      parsed.error\n    )\n  }\n\n  return parsed.data\n}\n\n/**\n * The keys of the configuration for a page.\n *\n * @internal - required to exclude zod types from the build\n */\nexport const PagesSegmentConfigSchemaKeys =\n  PagesSegmentConfigSchema.keyof().options\n\nexport type PagesSegmentConfigConfig = {\n  /**\n   * Enables AMP for the page.\n   */\n  amp?: boolean | 'hybrid'\n\n  /**\n   * The maximum duration for the page render.\n   */\n  maxDuration?: number\n\n  /**\n   * The runtime to use for the page.\n   */\n  runtime?: 'edge' | 'experimental-edge' | 'nodejs'\n\n  /**\n   * The preferred region for the page.\n   */\n  regions?: string[]\n}\n\nexport type PagesSegmentConfig = {\n  /**\n   * The runtime to use for the page.\n   */\n  runtime?: 'edge' | 'experimental-edge' | 'nodejs'\n\n  /**\n   * The maximum duration for the page render.\n   */\n  maxDuration?: number\n\n  /**\n   * The exported config object for the page.\n   */\n  config?: PagesSegmentConfigConfig\n}\n"], "names": ["z", "formatZodError", "PagesSegmentConfigSchema", "object", "runtime", "enum", "optional", "maxDuration", "number", "config", "amp", "union", "boolean", "literal", "parsePagesSegmentConfig", "data", "route", "parsed", "safeParse", "success", "error", "PagesSegmentConfigSchemaKeys", "keyof", "options"], "mappings": "AAAA,SAASA,CAAC,QAAQ,yBAAwB;AAC1C,SAASC,cAAc,QAAQ,0BAAyB;AAExD;;CAEC,GACD,MAAMC,2BAA2BF,EAAEG,MAAM,CAAC;IACxC;;GAEC,GACDC,SAASJ,EAAEK,IAAI,CAAC;QAAC;QAAQ;QAAqB;KAAS,EAAEC,QAAQ;IAEjE;;GAEC,GACDC,aAAaP,EAAEQ,MAAM,GAAGF,QAAQ;IAEhC;;GAEC,GACDG,QAAQT,EACLG,MAAM,CAAC;QACN;;OAEC,GACDO,KAAKV,EAAEW,KAAK,CAAC;YAACX,EAAEY,OAAO;YAAIZ,EAAEa,OAAO,CAAC;SAAU,EAAEP,QAAQ;QAEzD;;OAEC,GACDF,SAASJ,EAAEK,IAAI,CAAC;YAAC;YAAQ;YAAqB;SAAS,EAAEC,QAAQ;QAEjE;;OAEC,GACDC,aAAaP,EAAEQ,MAAM,GAAGF,QAAQ;IAClC,GACCA,QAAQ;AACb;AAEA;;;;;CAKC,GACD,OAAO,SAASQ,wBACdC,IAAa,EACbC,KAAa;IAEb,MAAMC,SAASf,yBAAyBgB,SAAS,CAACH,MAAM,CAAC;IACzD,IAAI,CAACE,OAAOE,OAAO,EAAE;QACnB,MAAMlB,eACJ,CAAC,oDAAoD,EAAEe,MAAM,oEAAoE,CAAC,EAClIC,OAAOG,KAAK;IAEhB;IAEA,OAAOH,OAAOF,IAAI;AACpB;AAEA;;;;CAIC,GACD,OAAO,MAAMM,+BACXnB,yBAAyBoB,KAAK,GAAGC,OAAO,CAAA"}