{"version": 3, "sources": ["../../../src/build/turborepo-access-trace/types.ts"], "sourcesContent": ["/**\n * A single Addr / Port pair that was accessed during the duration of the trace\n */\nexport interface Address {\n  addr: string\n  port: string\n}\n\n/**\n * Tracked environment variable keys that were accessed during the duration of the trace\n */\nexport type EnvVars = Set<string | Symbol>\n\n/**\n * Tracks the file system paths that were accessed during the duration of the trace\n */\nexport type FS = Set<string>\n\n/**\n * Tracked Addr / Port pairs that were accessed during the duration of the trace\n */\nexport type Addresses = Array<Address>\n\n/**\n * The serializable version of `TurborepoAccessTraceResult` - this is required to pass the `TurborepoAccessTraceResult`\n * between workers where Sets are not serializable.\n */\nexport type SerializableTurborepoAccessTraceResult = Readonly<{\n  fs: Array<string>\n  addresses: Addresses\n  envVars: Array<string>\n}>\n\n/**\n *  The public version of `TurborepoAccessTraceResult` - this is what is written to the trace file\n */\nexport type PublicTurborepoAccessTraceResult = Readonly<{\n  filePaths: Array<string>\n  network: boolean\n  envVarKeys: Array<string>\n}>\n\n/**\n * A function that restores the original state of a proxy\n */\nexport type RestoreOriginalFunction = () => void\n"], "names": [], "mappings": "AAAA;;CAEC,GAwCD;;CAEC,GACD,WAAgD"}