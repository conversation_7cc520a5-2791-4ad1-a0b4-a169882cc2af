{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-middleware-asset-loader.ts"], "sourcesContent": ["import loaderUtils from 'next/dist/compiled/loader-utils3'\nimport { getModuleBuildInfo } from './get-module-build-info'\n\nexport default function MiddlewareAssetLoader(this: any, source: Buffer) {\n  const name = loaderUtils.interpolateName(this, '[name].[hash].[ext]', {\n    context: this.rootContext,\n    content: source,\n  })\n  const filePath = `edge-chunks/asset_${name}`\n  const buildInfo = getModuleBuildInfo(this._module)\n  buildInfo.nextAssetMiddlewareBinding = {\n    filePath: `server/${filePath}`,\n    name,\n  }\n  this.emitFile(filePath, source)\n  return `module.exports = ${JSON.stringify(`blob:${name}`)}`\n}\n\nexport const raw = true\n"], "names": ["loaderUtils", "getModuleBuildInfo", "MiddlewareAssetLoader", "source", "name", "interpolateName", "context", "rootContext", "content", "filePath", "buildInfo", "_module", "nextAssetMiddlewareBinding", "emitFile", "JSON", "stringify", "raw"], "mappings": "AAAA,OAAOA,iBAAiB,mCAAkC;AAC1D,SAASC,kBAAkB,QAAQ,0BAAyB;AAE5D,eAAe,SAASC,sBAAiCC,MAAc;IACrE,MAAMC,OAAOJ,YAAYK,eAAe,CAAC,IAAI,EAAE,uBAAuB;QACpEC,SAAS,IAAI,CAACC,WAAW;QACzBC,SAASL;IACX;IACA,MAAMM,WAAW,CAAC,kBAAkB,EAAEL,MAAM;IAC5C,MAAMM,YAAYT,mBAAmB,IAAI,CAACU,OAAO;IACjDD,UAAUE,0BAA0B,GAAG;QACrCH,UAAU,CAAC,OAAO,EAAEA,UAAU;QAC9BL;IACF;IACA,IAAI,CAACS,QAAQ,CAACJ,UAAUN;IACxB,OAAO,CAAC,iBAAiB,EAAEW,KAAKC,SAAS,CAAC,CAAC,KAAK,EAAEX,MAAM,GAAG;AAC7D;AAEA,OAAO,MAAMY,MAAM,KAAI"}