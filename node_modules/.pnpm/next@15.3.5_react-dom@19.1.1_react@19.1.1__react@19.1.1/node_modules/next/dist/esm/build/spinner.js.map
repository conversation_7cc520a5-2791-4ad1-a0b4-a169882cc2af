{"version": 3, "sources": ["../../src/build/spinner.ts"], "sourcesContent": ["import ora from 'next/dist/compiled/ora'\nimport * as Log from './output/log'\n\nconst dotsSpinner = {\n  frames: ['.', '..', '...'],\n  interval: 200,\n}\n\nexport default function createSpinner(\n  text: string,\n  options: ora.Options = {},\n  logFn: (...data: any[]) => void = console.log\n) {\n  let spinner: undefined | (ora.Ora & { setText: (text: string) => void })\n\n  let prefixText = ` ${Log.prefixes.info} ${text} `\n\n  if (process.stdout.isTTY) {\n    spinner = ora({\n      text: undefined,\n      prefixText,\n      spinner: dotsSpinner,\n      stream: process.stdout,\n      ...options,\n    }).start() as ora.Ora & { setText: (text: string) => void }\n\n    // Add capturing of console.log/warn/error to allow pausing\n    // the spinner before logging and then restarting spinner after\n    const origLog = console.log\n    const origWarn = console.warn\n    const origError = console.error\n    const origStop = spinner.stop.bind(spinner)\n    const origStopAndPersist = spinner.stopAndPersist.bind(spinner)\n\n    const logHandle = (method: any, args: any[]) => {\n      // Enter a new line before logging new message, to avoid\n      // the new message shows up right after the spinner in the same line.\n      const isInProgress = spinner?.isSpinning\n      if (spinner && isInProgress) {\n        // Reset the current running spinner to empty line by `\\r`\n        spinner.prefixText = '\\r'\n        spinner.text = '\\r'\n        spinner.clear()\n        origStop()\n      }\n      method(...args)\n      if (spinner && isInProgress) {\n        spinner.start()\n      }\n    }\n\n    console.log = (...args: any) => logHandle(origLog, args)\n    console.warn = (...args: any) => logHandle(origWarn, args)\n    console.error = (...args: any) => logHandle(origError, args)\n\n    const resetLog = () => {\n      console.log = origLog\n      console.warn = origWarn\n      console.error = origError\n    }\n    spinner.setText = (newText) => {\n      text = newText\n      prefixText = ` ${Log.prefixes.info} ${newText} `\n      spinner!.prefixText = prefixText\n      return spinner!\n    }\n    spinner.stop = () => {\n      origStop()\n      resetLog()\n      return spinner!\n    }\n    spinner.stopAndPersist = () => {\n      // Add \\r at beginning to reset the current line of loading status text\n      const suffixText = `\\r ${Log.prefixes.event} ${text} `\n      if (spinner) {\n        spinner.text = suffixText\n      } else {\n        logFn(suffixText)\n      }\n      origStopAndPersist()\n      resetLog()\n      return spinner!\n    }\n  } else if (prefixText || text) {\n    logFn(prefixText ? prefixText + '...' : text)\n  }\n\n  return spinner\n}\n"], "names": ["ora", "Log", "dots<PERSON>pinner", "frames", "interval", "createSpinner", "text", "options", "logFn", "console", "log", "spinner", "prefixText", "prefixes", "info", "process", "stdout", "isTTY", "undefined", "stream", "start", "origLog", "origWarn", "warn", "origError", "error", "origStop", "stop", "bind", "origStopAndPersist", "stopAndPersist", "logHandle", "method", "args", "isInProgress", "isSpinning", "clear", "resetLog", "setText", "newText", "suffixText", "event"], "mappings": "AAAA,OAAOA,SAAS,yBAAwB;AACxC,YAAYC,SAAS,eAAc;AAEnC,MAAMC,cAAc;IAClBC,QAAQ;QAAC;QAAK;QAAM;KAAM;IAC1BC,UAAU;AACZ;AAEA,eAAe,SAASC,cACtBC,IAAY,EACZC,UAAuB,CAAC,CAAC,EACzBC,QAAkCC,QAAQC,GAAG;IAE7C,IAAIC;IAEJ,IAAIC,aAAa,CAAC,CAAC,EAAEX,IAAIY,QAAQ,CAACC,IAAI,CAAC,CAAC,EAAER,KAAK,CAAC,CAAC;IAEjD,IAAIS,QAAQC,MAAM,CAACC,KAAK,EAAE;QACxBN,UAAUX,IAAI;YACZM,MAAMY;YACNN;YACAD,SAAST;YACTiB,QAAQJ,QAAQC,MAAM;YACtB,GAAGT,OAAO;QACZ,GAAGa,KAAK;QAER,2DAA2D;QAC3D,+DAA+D;QAC/D,MAAMC,UAAUZ,QAAQC,GAAG;QAC3B,MAAMY,WAAWb,QAAQc,IAAI;QAC7B,MAAMC,YAAYf,QAAQgB,KAAK;QAC/B,MAAMC,WAAWf,QAAQgB,IAAI,CAACC,IAAI,CAACjB;QACnC,MAAMkB,qBAAqBlB,QAAQmB,cAAc,CAACF,IAAI,CAACjB;QAEvD,MAAMoB,YAAY,CAACC,QAAaC;YAC9B,wDAAwD;YACxD,qEAAqE;YACrE,MAAMC,eAAevB,2BAAAA,QAASwB,UAAU;YACxC,IAAIxB,WAAWuB,cAAc;gBAC3B,0DAA0D;gBAC1DvB,QAAQC,UAAU,GAAG;gBACrBD,QAAQL,IAAI,GAAG;gBACfK,QAAQyB,KAAK;gBACbV;YACF;YACAM,UAAUC;YACV,IAAItB,WAAWuB,cAAc;gBAC3BvB,QAAQS,KAAK;YACf;QACF;QAEAX,QAAQC,GAAG,GAAG,CAAC,GAAGuB,OAAcF,UAAUV,SAASY;QACnDxB,QAAQc,IAAI,GAAG,CAAC,GAAGU,OAAcF,UAAUT,UAAUW;QACrDxB,QAAQgB,KAAK,GAAG,CAAC,GAAGQ,OAAcF,UAAUP,WAAWS;QAEvD,MAAMI,WAAW;YACf5B,QAAQC,GAAG,GAAGW;YACdZ,QAAQc,IAAI,GAAGD;YACfb,QAAQgB,KAAK,GAAGD;QAClB;QACAb,QAAQ2B,OAAO,GAAG,CAACC;YACjBjC,OAAOiC;YACP3B,aAAa,CAAC,CAAC,EAAEX,IAAIY,QAAQ,CAACC,IAAI,CAAC,CAAC,EAAEyB,QAAQ,CAAC,CAAC;YAChD5B,QAASC,UAAU,GAAGA;YACtB,OAAOD;QACT;QACAA,QAAQgB,IAAI,GAAG;YACbD;YACAW;YACA,OAAO1B;QACT;QACAA,QAAQmB,cAAc,GAAG;YACvB,uEAAuE;YACvE,MAAMU,aAAa,CAAC,GAAG,EAAEvC,IAAIY,QAAQ,CAAC4B,KAAK,CAAC,CAAC,EAAEnC,KAAK,CAAC,CAAC;YACtD,IAAIK,SAAS;gBACXA,QAAQL,IAAI,GAAGkC;YACjB,OAAO;gBACLhC,MAAMgC;YACR;YACAX;YACAQ;YACA,OAAO1B;QACT;IACF,OAAO,IAAIC,cAAcN,MAAM;QAC7BE,MAAMI,aAAaA,aAAa,QAAQN;IAC1C;IAEA,OAAOK;AACT"}