{"version": 3, "sources": ["../../../src/build/output/log.ts"], "sourcesContent": ["import { bold, green, magenta, red, yellow, white } from '../../lib/picocolors'\nimport { LRUCache } from '../../server/lib/lru-cache'\n\nexport const prefixes = {\n  wait: white(bold('○')),\n  error: red(bold('⨯')),\n  warn: yellow(bold('⚠')),\n  ready: '▲', // no color\n  info: white(bold(' ')),\n  event: green(bold('✓')),\n  trace: magenta(bold('»')),\n} as const\n\nconst LOGGING_METHOD = {\n  log: 'log',\n  warn: 'warn',\n  error: 'error',\n} as const\n\nfunction prefixedLog(prefixType: keyof typeof prefixes, ...message: any[]) {\n  if ((message[0] === '' || message[0] === undefined) && message.length === 1) {\n    message.shift()\n  }\n\n  const consoleMethod: keyof typeof LOGGING_METHOD =\n    prefixType in LOGGING_METHOD\n      ? LOGGING_METHOD[prefixType as keyof typeof LOGGING_METHOD]\n      : 'log'\n\n  const prefix = prefixes[prefixType]\n  // If there's no message, don't print the prefix but a new line\n  if (message.length === 0) {\n    console[consoleMethod]('')\n  } else {\n    // Ensure if there's ANSI escape codes it's concatenated into one string.\n    // Chrome DevTool can only handle color if it's in one string.\n    if (message.length === 1 && typeof message[0] === 'string') {\n      console[consoleMethod](' ' + prefix + ' ' + message[0])\n    } else {\n      console[consoleMethod](' ' + prefix, ...message)\n    }\n  }\n}\n\nexport function bootstrap(...message: string[]) {\n  // logging format: ' <prefix> <message>'\n  // e.g. ' ✓ Compiled successfully'\n  // Add spaces to align with the indent of other logs\n  console.log('   ' + message.join(' '))\n}\n\nexport function wait(...message: any[]) {\n  prefixedLog('wait', ...message)\n}\n\nexport function error(...message: any[]) {\n  prefixedLog('error', ...message)\n}\n\nexport function warn(...message: any[]) {\n  prefixedLog('warn', ...message)\n}\n\nexport function ready(...message: any[]) {\n  prefixedLog('ready', ...message)\n}\n\nexport function info(...message: any[]) {\n  prefixedLog('info', ...message)\n}\n\nexport function event(...message: any[]) {\n  prefixedLog('event', ...message)\n}\n\nexport function trace(...message: any[]) {\n  prefixedLog('trace', ...message)\n}\n\nconst warnOnceCache = new LRUCache<string>(10_000, (value) => value.length)\nexport function warnOnce(...message: any[]) {\n  const key = message.join(' ')\n  if (!warnOnceCache.has(key)) {\n    warnOnceCache.set(key, key)\n    warn(...message)\n  }\n}\n"], "names": ["bold", "green", "magenta", "red", "yellow", "white", "L<PERSON><PERSON><PERSON>", "prefixes", "wait", "error", "warn", "ready", "info", "event", "trace", "LOGGING_METHOD", "log", "prefixedLog", "prefixType", "message", "undefined", "length", "shift", "consoleMethod", "prefix", "console", "bootstrap", "join", "warnOnceCache", "value", "warnOnce", "key", "has", "set"], "mappings": "AAAA,SAASA,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,QAAQ,uBAAsB;AAC/E,SAASC,QAAQ,QAAQ,6BAA4B;AAErD,OAAO,MAAMC,WAAW;IACtBC,MAAMH,MAAML,KAAK;IACjBS,OAAON,IAAIH,KAAK;IAChBU,MAAMN,OAAOJ,KAAK;IAClBW,OAAO;IACPC,MAAMP,MAAML,KAAK;IACjBa,OAAOZ,MAAMD,KAAK;IAClBc,OAAOZ,QAAQF,KAAK;AACtB,EAAU;AAEV,MAAMe,iBAAiB;IACrBC,KAAK;IACLN,MAAM;IACND,OAAO;AACT;AAEA,SAASQ,YAAYC,UAAiC,EAAE,GAAGC,OAAc;IACvE,IAAI,AAACA,CAAAA,OAAO,CAAC,EAAE,KAAK,MAAMA,OAAO,CAAC,EAAE,KAAKC,SAAQ,KAAMD,QAAQE,MAAM,KAAK,GAAG;QAC3EF,QAAQG,KAAK;IACf;IAEA,MAAMC,gBACJL,cAAcH,iBACVA,cAAc,CAACG,WAA0C,GACzD;IAEN,MAAMM,SAASjB,QAAQ,CAACW,WAAW;IACnC,+DAA+D;IAC/D,IAAIC,QAAQE,MAAM,KAAK,GAAG;QACxBI,OAAO,CAACF,cAAc,CAAC;IACzB,OAAO;QACL,yEAAyE;QACzE,8DAA8D;QAC9D,IAAIJ,QAAQE,MAAM,KAAK,KAAK,OAAOF,OAAO,CAAC,EAAE,KAAK,UAAU;YAC1DM,OAAO,CAACF,cAAc,CAAC,MAAMC,SAAS,MAAML,OAAO,CAAC,EAAE;QACxD,OAAO;YACLM,OAAO,CAACF,cAAc,CAAC,MAAMC,WAAWL;QAC1C;IACF;AACF;AAEA,OAAO,SAASO,UAAU,GAAGP,OAAiB;IAC5C,wCAAwC;IACxC,kCAAkC;IAClC,oDAAoD;IACpDM,QAAQT,GAAG,CAAC,QAAQG,QAAQQ,IAAI,CAAC;AACnC;AAEA,OAAO,SAASnB,KAAK,GAAGW,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEA,OAAO,SAASV,MAAM,GAAGU,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,OAAO,SAAST,KAAK,GAAGS,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEA,OAAO,SAASR,MAAM,GAAGQ,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,OAAO,SAASP,KAAK,GAAGO,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEA,OAAO,SAASN,MAAM,GAAGM,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,OAAO,SAASL,MAAM,GAAGK,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,MAAMS,gBAAgB,IAAItB,SAAiB,OAAQ,CAACuB,QAAUA,MAAMR,MAAM;AAC1E,OAAO,SAASS,SAAS,GAAGX,OAAc;IACxC,MAAMY,MAAMZ,QAAQQ,IAAI,CAAC;IACzB,IAAI,CAACC,cAAcI,GAAG,CAACD,MAAM;QAC3BH,cAAcK,GAAG,CAACF,KAAKA;QACvBrB,QAAQS;IACV;AACF"}