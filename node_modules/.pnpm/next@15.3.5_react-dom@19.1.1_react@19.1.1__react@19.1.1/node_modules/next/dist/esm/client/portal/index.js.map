{"version": 3, "sources": ["../../../src/client/portal/index.tsx"], "sourcesContent": ["import { useEffect, useState } from 'react'\nimport { createPortal } from 'react-dom'\n\ntype PortalProps = {\n  children: React.ReactNode\n  type: string\n}\n\nexport const Portal = ({ children, type }: PortalProps) => {\n  const [portalNode, setPortalNode] = useState<HTMLElement | null>(null)\n\n  useEffect(() => {\n    const element = document.createElement(type)\n    document.body.appendChild(element)\n    setPortalNode(element)\n    return () => {\n      document.body.removeChild(element)\n    }\n  }, [type])\n\n  return portalNode ? createPortal(children, portalNode) : null\n}\n"], "names": ["useEffect", "useState", "createPortal", "Portal", "children", "type", "portalNode", "setPortalNode", "element", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,QAAO;AAC3C,SAASC,YAAY,QAAQ,YAAW;AAOxC,OAAO,MAAMC,SAAS;QAAC,EAAEC,QAAQ,EAAEC,IAAI,EAAe;IACpD,MAAM,CAACC,YAAYC,cAAc,GAAGN,SAA6B;IAEjED,UAAU;QACR,MAAMQ,UAAUC,SAASC,aAAa,CAACL;QACvCI,SAASE,IAAI,CAACC,WAAW,CAACJ;QAC1BD,cAAcC;QACd,OAAO;YACLC,SAASE,IAAI,CAACE,WAAW,CAACL;QAC5B;IACF,GAAG;QAACH;KAAK;IAET,OAAOC,2BAAaJ,aAAaE,UAAUE,cAAc;AAC3D,EAAC"}