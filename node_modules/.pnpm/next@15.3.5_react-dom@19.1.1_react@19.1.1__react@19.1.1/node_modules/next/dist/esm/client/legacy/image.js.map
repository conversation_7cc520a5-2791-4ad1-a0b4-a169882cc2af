{"version": 3, "sources": ["../../../src/client/legacy/image.tsx"], "sourcesContent": ["'use client'\n\nimport React, {\n  useRef,\n  useEffect,\n  useCallback,\n  useContext,\n  useMemo,\n  useState,\n  type JSX,\n} from 'react'\nimport * as ReactDOM from 'react-dom'\nimport Head from '../../shared/lib/head'\nimport {\n  imageConfigDefault,\n  VALID_LOADERS,\n} from '../../shared/lib/image-config'\nimport type {\n  ImageConfigComplete,\n  LoaderValue,\n} from '../../shared/lib/image-config'\nimport { useIntersection } from '../use-intersection'\nimport { ImageConfigContext } from '../../shared/lib/image-config-context.shared-runtime'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport { normalizePathTrailingSlash } from '../normalize-trailing-slash'\n\nfunction normalizeSrc(src: string): string {\n  return src[0] === '/' ? src.slice(1) : src\n}\n\nconst supportsFloat = typeof ReactDOM.preload === 'function'\nconst DEFAULT_Q = 75\nconst configEnv = process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete\nconst loadedImageURLs = new Set<string>()\nconst allImgs = new Map<\n  string,\n  { src: string; priority: boolean; placeholder: string }\n>()\nlet perfObserver: PerformanceObserver | undefined\nconst emptyDataURL =\n  'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'\n\nif (typeof window === 'undefined') {\n  ;(globalThis as any).__NEXT_IMAGE_IMPORTED = true\n}\n\nconst VALID_LOADING_VALUES = ['lazy', 'eager', undefined] as const\ntype LoadingValue = (typeof VALID_LOADING_VALUES)[number]\ntype ImageConfig = ImageConfigComplete & { allSizes: number[] }\nexport type ImageLoader = (resolverProps: ImageLoaderProps) => string\n\nexport type ImageLoaderProps = {\n  src: string\n  width: number\n  quality?: number\n}\n\n// Do not export - this is an internal type only\n// because `next.config.js` is only meant for the\n// built-in loaders, not for a custom loader() prop.\ntype ImageLoaderWithConfig = (\n  resolverProps: ImageLoaderPropsWithConfig\n) => string\ntype ImageLoaderPropsWithConfig = ImageLoaderProps & {\n  config: Readonly<ImageConfig>\n}\n\nfunction imgixLoader({\n  config,\n  src,\n  width,\n  quality,\n}: ImageLoaderPropsWithConfig): string {\n  // Demo: https://static.imgix.net/daisy.png?auto=format&fit=max&w=300\n  const url = new URL(`${config.path}${normalizeSrc(src)}`)\n  const params = url.searchParams\n\n  // auto params can be combined with comma separation, or reiteration\n  params.set('auto', params.getAll('auto').join(',') || 'format')\n  params.set('fit', params.get('fit') || 'max')\n  params.set('w', params.get('w') || width.toString())\n\n  if (quality) {\n    params.set('q', quality.toString())\n  }\n\n  return url.href\n}\n\nfunction akamaiLoader({\n  config,\n  src,\n  width,\n}: ImageLoaderPropsWithConfig): string {\n  return `${config.path}${normalizeSrc(src)}?imwidth=${width}`\n}\n\nfunction cloudinaryLoader({\n  config,\n  src,\n  width,\n  quality,\n}: ImageLoaderPropsWithConfig): string {\n  // Demo: https://res.cloudinary.com/demo/image/upload/w_300,c_limit,q_auto/turtles.jpg\n  const params = ['f_auto', 'c_limit', 'w_' + width, 'q_' + (quality || 'auto')]\n  const paramsString = params.join(',') + '/'\n  return `${config.path}${paramsString}${normalizeSrc(src)}`\n}\n\nfunction customLoader({ src }: ImageLoaderProps): string {\n  throw new Error(\n    `Image with src \"${src}\" is missing \"loader\" prop.` +\n      `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader`\n  )\n}\n\nfunction defaultLoader({\n  config,\n  src,\n  width,\n  quality,\n}: ImageLoaderPropsWithConfig): string {\n  if (process.env.NODE_ENV !== 'production') {\n    const missingValues = []\n\n    // these should always be provided but make sure they are\n    if (!src) missingValues.push('src')\n    if (!width) missingValues.push('width')\n\n    if (missingValues.length > 0) {\n      throw new Error(\n        `Next Image Optimization requires ${missingValues.join(\n          ', '\n        )} to be provided. Make sure you pass them as props to the \\`next/image\\` component. Received: ${JSON.stringify(\n          { src, width, quality }\n        )}`\n      )\n    }\n\n    if (src.startsWith('//')) {\n      throw new Error(\n        `Failed to parse src \"${src}\" on \\`next/image\\`, protocol-relative URL (//) must be changed to an absolute URL (http:// or https://)`\n      )\n    }\n\n    if (src.startsWith('/') && config.localPatterns) {\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const {\n          hasLocalMatch,\n        } = require('../../shared/lib/match-local-pattern')\n        if (!hasLocalMatch(config.localPatterns, src)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\` does not match \\`images.localPatterns\\` configured in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-localpatterns`\n          )\n        }\n      }\n    }\n\n    if (!src.startsWith('/') && (config.domains || config.remotePatterns)) {\n      let parsedSrc: URL\n      try {\n        parsedSrc = new URL(src)\n      } catch (err) {\n        console.error(err)\n        throw new Error(\n          `Failed to parse src \"${src}\" on \\`next/image\\`, if using relative image it must start with a leading slash \"/\" or be an absolute URL (http:// or https://)`\n        )\n      }\n\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const {\n          hasRemoteMatch,\n        } = require('../../shared/lib/match-remote-pattern')\n        if (!hasRemoteMatch(config.domains, config.remotePatterns, parsedSrc)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\`, hostname \"${parsedSrc.hostname}\" is not configured under images in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-host`\n          )\n        }\n      }\n    }\n\n    if (quality && config.qualities && !config.qualities.includes(quality)) {\n      throw new Error(\n        `Invalid quality prop (${quality}) on \\`next/image\\` does not match \\`images.qualities\\` configured in your \\`next.config.js\\`\\n` +\n          `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-qualities`\n      )\n    }\n  }\n\n  const q =\n    quality ||\n    config.qualities?.reduce((prev, cur) =>\n      Math.abs(cur - DEFAULT_Q) < Math.abs(prev - DEFAULT_Q) ? cur : prev\n    ) ||\n    DEFAULT_Q\n\n  if (!config.dangerouslyAllowSVG && src.split('?', 1)[0].endsWith('.svg')) {\n    // Special case to make svg serve as-is to avoid proxying\n    // through the built-in Image Optimization API.\n    return src\n  }\n\n  return `${normalizePathTrailingSlash(config.path)}?url=${encodeURIComponent(\n    src\n  )}&w=${width}&q=${q}`\n}\n\nconst loaders = new Map<\n  LoaderValue,\n  (props: ImageLoaderPropsWithConfig) => string\n>([\n  ['default', defaultLoader],\n  ['imgix', imgixLoader],\n  ['cloudinary', cloudinaryLoader],\n  ['akamai', akamaiLoader],\n  ['custom', customLoader],\n])\n\nconst VALID_LAYOUT_VALUES = [\n  'fill',\n  'fixed',\n  'intrinsic',\n  'responsive',\n  undefined,\n] as const\ntype LayoutValue = (typeof VALID_LAYOUT_VALUES)[number]\n\ntype PlaceholderValue = 'blur' | 'empty'\n\ntype OnLoadingComplete = (result: {\n  naturalWidth: number\n  naturalHeight: number\n}) => void\n\ntype ImgElementStyle = NonNullable<JSX.IntrinsicElements['img']['style']>\n\ntype ImgElementWithDataProp = HTMLImageElement & {\n  'data-loaded-src': string | undefined\n}\n\nexport interface StaticImageData {\n  src: string\n  height: number\n  width: number\n  blurDataURL?: string\n}\n\ninterface StaticRequire {\n  default: StaticImageData\n}\n\ntype StaticImport = StaticRequire | StaticImageData\n\ntype SafeNumber = number | `${number}`\n\nfunction isStaticRequire(\n  src: StaticRequire | StaticImageData\n): src is StaticRequire {\n  return (src as StaticRequire).default !== undefined\n}\n\nfunction isStaticImageData(\n  src: StaticRequire | StaticImageData\n): src is StaticImageData {\n  return (src as StaticImageData).src !== undefined\n}\n\nfunction isStaticImport(src: string | StaticImport): src is StaticImport {\n  return (\n    typeof src === 'object' &&\n    (isStaticRequire(src as StaticImport) ||\n      isStaticImageData(src as StaticImport))\n  )\n}\n\nexport type ImageProps = Omit<\n  JSX.IntrinsicElements['img'],\n  'src' | 'srcSet' | 'ref' | 'width' | 'height' | 'loading'\n> & {\n  src: string | StaticImport\n  width?: SafeNumber\n  height?: SafeNumber\n  layout?: LayoutValue\n  loader?: ImageLoader\n  quality?: SafeNumber\n  priority?: boolean\n  loading?: LoadingValue\n  lazyRoot?: React.RefObject<HTMLElement | null> | null\n  lazyBoundary?: string\n  placeholder?: PlaceholderValue\n  blurDataURL?: string\n  unoptimized?: boolean\n  objectFit?: ImgElementStyle['objectFit']\n  objectPosition?: ImgElementStyle['objectPosition']\n  onLoadingComplete?: OnLoadingComplete\n}\n\ntype ImageElementProps = Omit<ImageProps, 'src' | 'loader'> & {\n  srcString: string\n  imgAttributes: GenImgAttrsResult\n  heightInt: number | undefined\n  widthInt: number | undefined\n  qualityInt: number | undefined\n  layout: LayoutValue\n  imgStyle: ImgElementStyle\n  blurStyle: ImgElementStyle\n  isLazy: boolean\n  loading: LoadingValue\n  config: ImageConfig\n  unoptimized: boolean\n  loader: ImageLoaderWithConfig\n  placeholder: PlaceholderValue\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>\n  setBlurComplete: (b: boolean) => void\n  setIntersection: (img: HTMLImageElement | null) => void\n  isVisible: boolean\n  noscriptSizes: string | undefined\n}\n\nfunction getWidths(\n  { deviceSizes, allSizes }: ImageConfig,\n  width: number | undefined,\n  layout: LayoutValue,\n  sizes: string | undefined\n): { widths: number[]; kind: 'w' | 'x' } {\n  if (sizes && (layout === 'fill' || layout === 'responsive')) {\n    // Find all the \"vw\" percent sizes used in the sizes prop\n    const viewportWidthRe = /(^|\\s)(1?\\d?\\d)vw/g\n    const percentSizes = []\n    for (let match; (match = viewportWidthRe.exec(sizes)); match) {\n      percentSizes.push(parseInt(match[2]))\n    }\n    if (percentSizes.length) {\n      const smallestRatio = Math.min(...percentSizes) * 0.01\n      return {\n        widths: allSizes.filter((s) => s >= deviceSizes[0] * smallestRatio),\n        kind: 'w',\n      }\n    }\n    return { widths: allSizes, kind: 'w' }\n  }\n  if (\n    typeof width !== 'number' ||\n    layout === 'fill' ||\n    layout === 'responsive'\n  ) {\n    return { widths: deviceSizes, kind: 'w' }\n  }\n\n  const widths = [\n    ...new Set(\n      // > This means that most OLED screens that say they are 3x resolution,\n      // > are actually 3x in the green color, but only 1.5x in the red and\n      // > blue colors. Showing a 3x resolution image in the app vs a 2x\n      // > resolution image will be visually the same, though the 3x image\n      // > takes significantly more data. Even true 3x resolution screens are\n      // > wasteful as the human eye cannot see that level of detail without\n      // > something like a magnifying glass.\n      // https://blog.twitter.com/engineering/en_us/topics/infrastructure/2019/capping-image-fidelity-on-ultra-high-resolution-devices.html\n      [width, width * 2 /*, width * 3*/].map(\n        (w) => allSizes.find((p) => p >= w) || allSizes[allSizes.length - 1]\n      )\n    ),\n  ]\n  return { widths, kind: 'x' }\n}\n\ntype GenImgAttrsData = {\n  config: ImageConfig\n  src: string\n  unoptimized: boolean\n  layout: LayoutValue\n  loader: ImageLoaderWithConfig\n  width?: number\n  quality?: number\n  sizes?: string\n}\n\ntype GenImgAttrsResult = {\n  src: string\n  srcSet: string | undefined\n  sizes: string | undefined\n}\n\nfunction generateImgAttrs({\n  config,\n  src,\n  unoptimized,\n  layout,\n  width,\n  quality,\n  sizes,\n  loader,\n}: GenImgAttrsData): GenImgAttrsResult {\n  if (unoptimized) {\n    return { src, srcSet: undefined, sizes: undefined }\n  }\n\n  const { widths, kind } = getWidths(config, width, layout, sizes)\n  const last = widths.length - 1\n\n  return {\n    sizes: !sizes && kind === 'w' ? '100vw' : sizes,\n    srcSet: widths\n      .map(\n        (w, i) =>\n          `${loader({ config, src, quality, width: w })} ${\n            kind === 'w' ? w : i + 1\n          }${kind}`\n      )\n      .join(', '),\n\n    // It's intended to keep `src` the last attribute because React updates\n    // attributes in order. If we keep `src` the first one, Safari will\n    // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n    // updated by React. That causes multiple unnecessary requests if `srcSet`\n    // and `sizes` are defined.\n    // This bug cannot be reproduced in Chrome or Firefox.\n    src: loader({ config, src, quality, width: widths[last] }),\n  }\n}\n\nfunction getInt(x: unknown): number | undefined {\n  if (typeof x === 'number') {\n    return x\n  }\n  if (typeof x === 'string') {\n    return parseInt(x, 10)\n  }\n  return undefined\n}\n\nfunction defaultImageLoader(loaderProps: ImageLoaderPropsWithConfig) {\n  const loaderKey = loaderProps.config?.loader || 'default'\n  const load = loaders.get(loaderKey)\n  if (load) {\n    return load(loaderProps)\n  }\n  throw new Error(\n    `Unknown \"loader\" found in \"next.config.js\". Expected: ${VALID_LOADERS.join(\n      ', '\n    )}. Received: ${loaderKey}`\n  )\n}\n\n// See https://stackoverflow.com/q/39777833/266535 for why we use this ref\n// handler instead of the img's onLoad attribute.\nfunction handleLoading(\n  img: ImgElementWithDataProp,\n  src: string,\n  layout: LayoutValue,\n  placeholder: PlaceholderValue,\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>,\n  setBlurComplete: (b: boolean) => void\n) {\n  if (!img || img.src === emptyDataURL || img['data-loaded-src'] === src) {\n    return\n  }\n  img['data-loaded-src'] = src\n  const p = 'decode' in img ? img.decode() : Promise.resolve()\n  p.catch(() => {}).then(() => {\n    if (!img.parentNode) {\n      // Exit early in case of race condition:\n      // - onload() is called\n      // - decode() is called but incomplete\n      // - unmount is called\n      // - decode() completes\n      return\n    }\n    loadedImageURLs.add(src)\n    if (placeholder === 'blur') {\n      setBlurComplete(true)\n    }\n    if (onLoadingCompleteRef?.current) {\n      const { naturalWidth, naturalHeight } = img\n      // Pass back read-only primitive values but not the\n      // underlying DOM element because it could be misused.\n      onLoadingCompleteRef.current({ naturalWidth, naturalHeight })\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (img.parentElement?.parentElement) {\n        const parent = getComputedStyle(img.parentElement.parentElement)\n        if (!parent.position) {\n          // The parent has not been rendered to the dom yet and therefore it has no position. Skip the warnings for such cases.\n        } else if (layout === 'responsive' && parent.display === 'flex') {\n          warnOnce(\n            `Image with src \"${src}\" may not render properly as a child of a flex container. Consider wrapping the image with a div to configure the width.`\n          )\n        } else if (\n          layout === 'fill' &&\n          parent.position !== 'relative' &&\n          parent.position !== 'fixed' &&\n          parent.position !== 'absolute'\n        ) {\n          warnOnce(\n            `Image with src \"${src}\" may not render properly with a parent using position:\"${parent.position}\". Consider changing the parent style to position:\"relative\" with a width and height.`\n          )\n        }\n      }\n    }\n  })\n}\n\nconst ImageElement = ({\n  imgAttributes,\n  heightInt,\n  widthInt,\n  qualityInt,\n  layout,\n  className,\n  imgStyle,\n  blurStyle,\n  isLazy,\n  placeholder,\n  loading,\n  srcString,\n  config,\n  unoptimized,\n  loader,\n  onLoadingCompleteRef,\n  setBlurComplete,\n  setIntersection,\n  onLoad,\n  onError,\n  isVisible,\n  noscriptSizes,\n  ...rest\n}: ImageElementProps) => {\n  loading = isLazy ? 'lazy' : loading\n  return (\n    <>\n      <img\n        {...rest}\n        {...imgAttributes}\n        decoding=\"async\"\n        data-nimg={layout}\n        className={className}\n        style={{ ...imgStyle, ...blurStyle }}\n        ref={useCallback(\n          (img: ImgElementWithDataProp) => {\n            if (process.env.NODE_ENV !== 'production') {\n              if (img && !srcString) {\n                console.error(`Image is missing required \"src\" property:`, img)\n              }\n            }\n            setIntersection(img)\n            if (img?.complete) {\n              handleLoading(\n                img,\n                srcString,\n                layout,\n                placeholder,\n                onLoadingCompleteRef,\n                setBlurComplete\n              )\n            }\n          },\n          [\n            setIntersection,\n            srcString,\n            layout,\n            placeholder,\n            onLoadingCompleteRef,\n            setBlurComplete,\n          ]\n        )}\n        onLoad={(event) => {\n          const img = event.currentTarget as ImgElementWithDataProp\n          handleLoading(\n            img,\n            srcString,\n            layout,\n            placeholder,\n            onLoadingCompleteRef,\n            setBlurComplete\n          )\n          if (onLoad) {\n            onLoad(event)\n          }\n        }}\n        onError={(event) => {\n          if (placeholder === 'blur') {\n            // If the real image fails to load, this will still remove the placeholder.\n            setBlurComplete(true)\n          }\n          if (onError) {\n            onError(event)\n          }\n        }}\n      />\n      {(isLazy || placeholder === 'blur') && (\n        <noscript>\n          <img\n            {...rest}\n            // @ts-ignore - TODO: upgrade to `@types/react@17`\n            loading={loading}\n            decoding=\"async\"\n            data-nimg={layout}\n            style={imgStyle}\n            className={className}\n            // It's intended to keep `loading` before `src` because React updates\n            // props in order which causes Safari/Firefox to not lazy load properly.\n            // See https://github.com/facebook/react/issues/25883\n            {...generateImgAttrs({\n              config,\n              src: srcString,\n              unoptimized,\n              layout,\n              width: widthInt,\n              quality: qualityInt,\n              sizes: noscriptSizes,\n              loader,\n            })}\n          />\n        </noscript>\n      )}\n    </>\n  )\n}\n\nexport default function Image({\n  src,\n  sizes,\n  unoptimized = false,\n  priority = false,\n  loading,\n  lazyRoot = null,\n  lazyBoundary,\n  className,\n  quality,\n  width,\n  height,\n  style,\n  objectFit,\n  objectPosition,\n  onLoadingComplete,\n  placeholder = 'empty',\n  blurDataURL,\n  ...all\n}: ImageProps) {\n  const configContext = useContext(ImageConfigContext)\n  const config: ImageConfig = useMemo(() => {\n    const c = configEnv || configContext || imageConfigDefault\n    const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n    const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n    const qualities = c.qualities?.sort((a, b) => a - b)\n    return { ...c, allSizes, deviceSizes, qualities }\n  }, [configContext])\n\n  let rest: Partial<ImageProps> = all\n  let layout: NonNullable<LayoutValue> = sizes ? 'responsive' : 'intrinsic'\n  if ('layout' in rest) {\n    // Override default layout if the user specified one:\n    if (rest.layout) layout = rest.layout\n\n    // Remove property so it's not spread on <img>:\n    delete rest.layout\n  }\n\n  let loader: ImageLoaderWithConfig = defaultImageLoader\n  if ('loader' in rest) {\n    if (rest.loader) {\n      const customImageLoader = rest.loader\n      loader = (obj) => {\n        const { config: _, ...opts } = obj\n        // The config object is internal only so we must\n        // not pass it to the user-defined loader()\n        return customImageLoader(opts)\n      }\n    }\n    // Remove property so it's not spread on <img>\n    delete rest.loader\n  }\n\n  let staticSrc = ''\n  if (isStaticImport(src)) {\n    const staticImageData = isStaticRequire(src) ? src.default : src\n\n    if (!staticImageData.src) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n    blurDataURL = blurDataURL || staticImageData.blurDataURL\n    staticSrc = staticImageData.src\n    if (!layout || layout !== 'fill') {\n      height = height || staticImageData.height\n      width = width || staticImageData.width\n      if (!staticImageData.height || !staticImageData.width) {\n        throw new Error(\n          `An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received ${JSON.stringify(\n            staticImageData\n          )}`\n        )\n      }\n    }\n  }\n  src = typeof src === 'string' ? src : staticSrc\n\n  let isLazy =\n    !priority && (loading === 'lazy' || typeof loading === 'undefined')\n  if (src.startsWith('data:') || src.startsWith('blob:')) {\n    // https://developer.mozilla.org/docs/Web/HTTP/Basics_of_HTTP/Data_URIs\n    unoptimized = true\n    isLazy = false\n  }\n  if (typeof window !== 'undefined' && loadedImageURLs.has(src)) {\n    isLazy = false\n  }\n  if (config.unoptimized) {\n    unoptimized = true\n  }\n\n  const [blurComplete, setBlurComplete] = useState(false)\n  const [setIntersection, isIntersected, resetIntersected] =\n    useIntersection<HTMLImageElement>({\n      rootRef: lazyRoot,\n      rootMargin: lazyBoundary || '200px',\n      disabled: !isLazy,\n    })\n  const isVisible = !isLazy || isIntersected\n\n  const wrapperStyle: JSX.IntrinsicElements['span']['style'] = {\n    boxSizing: 'border-box',\n    display: 'block',\n    overflow: 'hidden',\n    width: 'initial',\n    height: 'initial',\n    background: 'none',\n    opacity: 1,\n    border: 0,\n    margin: 0,\n    padding: 0,\n  }\n  const sizerStyle: JSX.IntrinsicElements['span']['style'] = {\n    boxSizing: 'border-box',\n    display: 'block',\n    width: 'initial',\n    height: 'initial',\n    background: 'none',\n    opacity: 1,\n    border: 0,\n    margin: 0,\n    padding: 0,\n  }\n  let hasSizer = false\n  let sizerSvgUrl: string | undefined\n  const layoutStyle: ImgElementStyle = {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    bottom: 0,\n    right: 0,\n\n    boxSizing: 'border-box',\n    padding: 0,\n    border: 'none',\n    margin: 'auto',\n\n    display: 'block',\n    width: 0,\n    height: 0,\n    minWidth: '100%',\n    maxWidth: '100%',\n    minHeight: '100%',\n    maxHeight: '100%',\n\n    objectFit,\n    objectPosition,\n  }\n\n  let widthInt = getInt(width)\n  let heightInt = getInt(height)\n  const qualityInt = getInt(quality)\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (!src) {\n      // React doesn't show the stack trace and there's\n      // no `src` to help identify which image, so we\n      // instead console.error(ref) during mount.\n      widthInt = widthInt || 1\n      heightInt = heightInt || 1\n      unoptimized = true\n    } else {\n      if (!VALID_LAYOUT_VALUES.includes(layout)) {\n        throw new Error(\n          `Image with src \"${src}\" has invalid \"layout\" property. Provided \"${layout}\" should be one of ${VALID_LAYOUT_VALUES.map(\n            String\n          ).join(',')}.`\n        )\n      }\n\n      if (\n        (typeof widthInt !== 'undefined' && isNaN(widthInt)) ||\n        (typeof heightInt !== 'undefined' && isNaN(heightInt))\n      ) {\n        throw new Error(\n          `Image with src \"${src}\" has invalid \"width\" or \"height\" property. These should be numeric values.`\n        )\n      }\n      if (layout === 'fill' && (width || height)) {\n        warnOnce(\n          `Image with src \"${src}\" and \"layout='fill'\" has unused properties assigned. Please remove \"width\" and \"height\".`\n        )\n      }\n      if (!VALID_LOADING_VALUES.includes(loading)) {\n        throw new Error(\n          `Image with src \"${src}\" has invalid \"loading\" property. Provided \"${loading}\" should be one of ${VALID_LOADING_VALUES.map(\n            String\n          ).join(',')}.`\n        )\n      }\n      if (priority && loading === 'lazy') {\n        throw new Error(\n          `Image with src \"${src}\" has both \"priority\" and \"loading='lazy'\" properties. Only one should be used.`\n        )\n      }\n      if (sizes && layout !== 'fill' && layout !== 'responsive') {\n        warnOnce(\n          `Image with src \"${src}\" has \"sizes\" property but it will be ignored. Only use \"sizes\" with \"layout='fill'\" or \"layout='responsive'\"`\n        )\n      }\n      if (placeholder === 'blur') {\n        if (layout !== 'fill' && (widthInt || 0) * (heightInt || 0) < 1600) {\n          warnOnce(\n            `Image with src \"${src}\" is smaller than 40x40. Consider removing the \"placeholder='blur'\" property to improve performance.`\n          )\n        }\n        if (!blurDataURL) {\n          const VALID_BLUR_EXT = ['jpeg', 'png', 'webp', 'avif'] // should match next-image-loader\n\n          throw new Error(\n            `Image with src \"${src}\" has \"placeholder='blur'\" property but is missing the \"blurDataURL\" property.\n          Possible solutions:\n            - Add a \"blurDataURL\" property, the contents should be a small Data URL to represent the image\n            - Change the \"src\" property to a static import with one of the supported file types: ${VALID_BLUR_EXT.join(\n              ','\n            )} (animated images not supported)\n            - Remove the \"placeholder\" property, effectively no blur effect\n          Read more: https://nextjs.org/docs/messages/placeholder-blur-data-url`\n          )\n        }\n      }\n      if ('ref' in rest) {\n        warnOnce(\n          `Image with src \"${src}\" is using unsupported \"ref\" property. Consider using the \"onLoadingComplete\" property instead.`\n        )\n      }\n\n      if (!unoptimized && loader !== defaultImageLoader) {\n        const urlStr = loader({\n          config,\n          src,\n          width: widthInt || 400,\n          quality: qualityInt || 75,\n        })\n        let url: URL | undefined\n        try {\n          url = new URL(urlStr)\n        } catch (err) {}\n        if (urlStr === src || (url && url.pathname === src && !url.search)) {\n          warnOnce(\n            `Image with src \"${src}\" has a \"loader\" property that does not implement width. Please implement it or use the \"unoptimized\" property instead.` +\n              `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader-width`\n          )\n        }\n      }\n\n      if (style) {\n        let overwrittenStyles = Object.keys(style).filter(\n          (key) => key in layoutStyle\n        )\n        if (overwrittenStyles.length) {\n          warnOnce(\n            `Image with src ${src} is assigned the following styles, which are overwritten by automatically-generated styles: ${overwrittenStyles.join(\n              ', '\n            )}`\n          )\n        }\n      }\n\n      if (\n        typeof window !== 'undefined' &&\n        !perfObserver &&\n        window.PerformanceObserver\n      ) {\n        perfObserver = new PerformanceObserver((entryList) => {\n          for (const entry of entryList.getEntries()) {\n            // @ts-ignore - missing \"LargestContentfulPaint\" class with \"element\" prop\n            const imgSrc = entry?.element?.src || ''\n            const lcpImage = allImgs.get(imgSrc)\n            if (\n              lcpImage &&\n              !lcpImage.priority &&\n              lcpImage.placeholder !== 'blur' &&\n              !lcpImage.src.startsWith('data:') &&\n              !lcpImage.src.startsWith('blob:')\n            ) {\n              // https://web.dev/lcp/#measure-lcp-in-javascript\n              warnOnce(\n                `Image with src \"${lcpImage.src}\" was detected as the Largest Contentful Paint (LCP). Please add the \"priority\" property if this image is above the fold.` +\n                  `\\nRead more: https://nextjs.org/docs/api-reference/next/legacy/image#priority`\n              )\n            }\n          }\n        })\n        try {\n          perfObserver.observe({\n            type: 'largest-contentful-paint',\n            buffered: true,\n          })\n        } catch (err) {\n          // Log error but don't crash the app\n          console.error(err)\n        }\n      }\n    }\n  }\n  const imgStyle = Object.assign({}, style, layoutStyle)\n  const blurStyle =\n    placeholder === 'blur' && !blurComplete\n      ? {\n          backgroundSize: objectFit || 'cover',\n          backgroundPosition: objectPosition || '0% 0%',\n          filter: 'blur(20px)',\n          backgroundImage: `url(\"${blurDataURL}\")`,\n        }\n      : {}\n  if (layout === 'fill') {\n    // <Image src=\"i.png\" layout=\"fill\" />\n    wrapperStyle.display = 'block'\n    wrapperStyle.position = 'absolute'\n    wrapperStyle.top = 0\n    wrapperStyle.left = 0\n    wrapperStyle.bottom = 0\n    wrapperStyle.right = 0\n  } else if (\n    typeof widthInt !== 'undefined' &&\n    typeof heightInt !== 'undefined'\n  ) {\n    // <Image src=\"i.png\" width=\"100\" height=\"100\" />\n    const quotient = heightInt / widthInt\n    const paddingTop = isNaN(quotient) ? '100%' : `${quotient * 100}%`\n    if (layout === 'responsive') {\n      // <Image src=\"i.png\" width=\"100\" height=\"100\" layout=\"responsive\" />\n      wrapperStyle.display = 'block'\n      wrapperStyle.position = 'relative'\n      hasSizer = true\n      sizerStyle.paddingTop = paddingTop\n    } else if (layout === 'intrinsic') {\n      // <Image src=\"i.png\" width=\"100\" height=\"100\" layout=\"intrinsic\" />\n      wrapperStyle.display = 'inline-block'\n      wrapperStyle.position = 'relative'\n      wrapperStyle.maxWidth = '100%'\n      hasSizer = true\n      sizerStyle.maxWidth = '100%'\n      sizerSvgUrl = `data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%27${widthInt}%27%20height=%27${heightInt}%27/%3e`\n    } else if (layout === 'fixed') {\n      // <Image src=\"i.png\" width=\"100\" height=\"100\" layout=\"fixed\" />\n      wrapperStyle.display = 'inline-block'\n      wrapperStyle.position = 'relative'\n      wrapperStyle.width = widthInt\n      wrapperStyle.height = heightInt\n    }\n  } else {\n    // <Image src=\"i.png\" />\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(\n        `Image with src \"${src}\" must use \"width\" and \"height\" properties or \"layout='fill'\" property.`\n      )\n    }\n  }\n\n  let imgAttributes: GenImgAttrsResult = {\n    src: emptyDataURL,\n    srcSet: undefined,\n    sizes: undefined,\n  }\n\n  if (isVisible) {\n    imgAttributes = generateImgAttrs({\n      config,\n      src,\n      unoptimized,\n      layout,\n      width: widthInt,\n      quality: qualityInt,\n      sizes,\n      loader,\n    })\n  }\n\n  let srcString: string = src\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof window !== 'undefined') {\n      let fullUrl: URL\n      try {\n        fullUrl = new URL(imgAttributes.src)\n      } catch (e) {\n        fullUrl = new URL(imgAttributes.src, window.location.href)\n      }\n      allImgs.set(fullUrl.href, { src, priority, placeholder })\n    }\n  }\n\n  const linkProps:\n    | React.DetailedHTMLProps<\n        React.LinkHTMLAttributes<HTMLLinkElement>,\n        HTMLLinkElement\n      >\n    | undefined = supportsFloat\n    ? undefined\n    : {\n        imageSrcSet: imgAttributes.srcSet,\n        imageSizes: imgAttributes.sizes,\n        crossOrigin: rest.crossOrigin,\n        referrerPolicy: rest.referrerPolicy,\n      }\n\n  const useLayoutEffect =\n    typeof window === 'undefined' ? React.useEffect : React.useLayoutEffect\n  const onLoadingCompleteRef = useRef(onLoadingComplete)\n\n  const previousImageSrc = useRef<string | StaticImport>(src)\n  useEffect(() => {\n    onLoadingCompleteRef.current = onLoadingComplete\n  }, [onLoadingComplete])\n\n  useLayoutEffect(() => {\n    if (previousImageSrc.current !== src) {\n      resetIntersected()\n      previousImageSrc.current = src\n    }\n  }, [resetIntersected, src])\n\n  const imgElementArgs = {\n    isLazy,\n    imgAttributes,\n    heightInt,\n    widthInt,\n    qualityInt,\n    layout,\n    className,\n    imgStyle,\n    blurStyle,\n    loading,\n    config,\n    unoptimized,\n    placeholder,\n    loader,\n    srcString,\n    onLoadingCompleteRef,\n    setBlurComplete,\n    setIntersection,\n    isVisible,\n    noscriptSizes: sizes,\n    ...rest,\n  }\n  return (\n    <>\n      <span style={wrapperStyle}>\n        {hasSizer ? (\n          <span style={sizerStyle}>\n            {sizerSvgUrl ? (\n              <img\n                style={{\n                  display: 'block',\n                  maxWidth: '100%',\n                  width: 'initial',\n                  height: 'initial',\n                  background: 'none',\n                  opacity: 1,\n                  border: 0,\n                  margin: 0,\n                  padding: 0,\n                }}\n                alt=\"\"\n                aria-hidden={true}\n                src={sizerSvgUrl}\n              />\n            ) : null}\n          </span>\n        ) : null}\n        <ImageElement {...imgElementArgs} />\n      </span>\n      {!supportsFloat && priority ? (\n        // Note how we omit the `href` attribute, as it would only be relevant\n        // for browsers that do not support `imagesrcset`, and in those cases\n        // it would likely cause the incorrect image to be preloaded.\n        //\n        // https://html.spec.whatwg.org/multipage/semantics.html#attr-link-imagesrcset\n        <Head>\n          <link\n            key={\n              '__nimg-' +\n              imgAttributes.src +\n              imgAttributes.srcSet +\n              imgAttributes.sizes\n            }\n            rel=\"preload\"\n            as=\"image\"\n            href={imgAttributes.srcSet ? undefined : imgAttributes.src}\n            {...linkProps}\n          />\n        </Head>\n      ) : null}\n    </>\n  )\n}\n"], "names": ["React", "useRef", "useEffect", "useCallback", "useContext", "useMemo", "useState", "ReactDOM", "Head", "imageConfigDefault", "VALID_LOADERS", "useIntersection", "ImageConfigContext", "warnOnce", "normalizePathTrailingSlash", "normalizeSrc", "src", "slice", "supportsFloat", "preload", "DEFAULT_Q", "configEnv", "process", "env", "__NEXT_IMAGE_OPTS", "loadedImageURLs", "Set", "allImgs", "Map", "perfObserver", "emptyDataURL", "window", "globalThis", "__NEXT_IMAGE_IMPORTED", "VALID_LOADING_VALUES", "undefined", "imgixLoader", "config", "width", "quality", "url", "URL", "path", "params", "searchParams", "set", "getAll", "join", "get", "toString", "href", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramsString", "customLoader", "Error", "defaultLoader", "NODE_ENV", "<PERSON><PERSON><PERSON><PERSON>", "push", "length", "JSON", "stringify", "startsWith", "localPatterns", "NEXT_RUNTIME", "hasLocalMatch", "require", "domains", "remotePatterns", "parsedSrc", "err", "console", "error", "hasRemoteMatch", "hostname", "qualities", "includes", "q", "reduce", "prev", "cur", "Math", "abs", "dangerouslyAllowSVG", "split", "endsWith", "encodeURIComponent", "loaders", "VALID_LAYOUT_VALUES", "isStaticRequire", "default", "isStaticImageData", "isStaticImport", "getWidths", "layout", "sizes", "deviceSizes", "allSizes", "viewportWidthRe", "percentSizes", "match", "exec", "parseInt", "smallestRatio", "min", "widths", "filter", "s", "kind", "map", "w", "find", "p", "generateImgAttrs", "unoptimized", "loader", "srcSet", "last", "i", "getInt", "x", "defaultImageLoader", "loaderProps", "loader<PERSON>ey", "load", "handleLoading", "img", "placeholder", "onLoadingCompleteRef", "setBlurComplete", "decode", "Promise", "resolve", "catch", "then", "parentNode", "add", "current", "naturalWidth", "naturalHeight", "parentElement", "parent", "getComputedStyle", "position", "display", "ImageElement", "imgAttributes", "heightInt", "widthInt", "qualityInt", "className", "imgStyle", "blurStyle", "isLazy", "loading", "srcString", "setIntersection", "onLoad", "onError", "isVisible", "noscriptSizes", "rest", "decoding", "data-nimg", "style", "ref", "complete", "event", "currentTarget", "noscript", "Image", "priority", "lazyRoot", "lazyBoundary", "height", "objectFit", "objectPosition", "onLoadingComplete", "blurDataURL", "all", "configContext", "c", "imageSizes", "sort", "a", "b", "customImageLoader", "obj", "_", "opts", "staticSrc", "staticImageData", "has", "blurComplete", "isIntersected", "resetIntersected", "rootRef", "rootMargin", "disabled", "wrapperStyle", "boxSizing", "overflow", "background", "opacity", "border", "margin", "padding", "sizerStyle", "hasSizer", "sizerSvgUrl", "layoutStyle", "top", "left", "bottom", "right", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minHeight", "maxHeight", "String", "isNaN", "VALID_BLUR_EXT", "urlStr", "pathname", "search", "overwrittenStyles", "Object", "keys", "key", "PerformanceObserver", "entryList", "entry", "getEntries", "imgSrc", "element", "lcpImage", "observe", "type", "buffered", "assign", "backgroundSize", "backgroundPosition", "backgroundImage", "quotient", "paddingTop", "fullUrl", "e", "location", "linkProps", "imageSrcSet", "crossOrigin", "referrerPolicy", "useLayoutEffect", "previousImageSrc", "imgElementArgs", "span", "alt", "aria-hidden", "link", "rel", "as"], "mappings": "AAAA;;AAEA,OAAOA,SACLC,MAAM,EACNC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,OAAO,EACPC,QAAQ,QAEH,QAAO;AACd,YAAYC,cAAc,YAAW;AACrC,OAAOC,UAAU,wBAAuB;AACxC,SACEC,kBAAkB,EAClBC,aAAa,QACR,gCAA+B;AAKtC,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,kBAAkB,QAAQ,uDAAsD;AACzF,SAASC,QAAQ,QAAQ,mCAAkC;AAC3D,SAASC,0BAA0B,QAAQ,8BAA6B;AAExE,SAASC,aAAaC,GAAW;IAC/B,OAAOA,GAAG,CAAC,EAAE,KAAK,MAAMA,IAAIC,KAAK,CAAC,KAAKD;AACzC;AAEA,MAAME,gBAAgB,OAAOX,SAASY,OAAO,KAAK;AAClD,MAAMC,YAAY;AAClB,MAAMC,YAAYC,QAAQC,GAAG,CAACC,iBAAiB;AAC/C,MAAMC,kBAAkB,IAAIC;AAC5B,MAAMC,UAAU,IAAIC;AAIpB,IAAIC;AACJ,MAAMC,eACJ;AAEF,IAAI,OAAOC,WAAW,aAAa;;IAC/BC,WAAmBC,qBAAqB,GAAG;AAC/C;AAEA,MAAMC,uBAAuB;IAAC;IAAQ;IAASC;CAAU;AAqBzD,SAASC,YAAY,KAKQ;IALR,IAAA,EACnBC,MAAM,EACNrB,GAAG,EACHsB,KAAK,EACLC,OAAO,EACoB,GALR;IAMnB,qEAAqE;IACrE,MAAMC,MAAM,IAAIC,IAAI,AAAC,KAAEJ,OAAOK,IAAI,GAAG3B,aAAaC;IAClD,MAAM2B,SAASH,IAAII,YAAY;IAE/B,oEAAoE;IACpED,OAAOE,GAAG,CAAC,QAAQF,OAAOG,MAAM,CAAC,QAAQC,IAAI,CAAC,QAAQ;IACtDJ,OAAOE,GAAG,CAAC,OAAOF,OAAOK,GAAG,CAAC,UAAU;IACvCL,OAAOE,GAAG,CAAC,KAAKF,OAAOK,GAAG,CAAC,QAAQV,MAAMW,QAAQ;IAEjD,IAAIV,SAAS;QACXI,OAAOE,GAAG,CAAC,KAAKN,QAAQU,QAAQ;IAClC;IAEA,OAAOT,IAAIU,IAAI;AACjB;AAEA,SAASC,aAAa,KAIO;IAJP,IAAA,EACpBd,MAAM,EACNrB,GAAG,EACHsB,KAAK,EACsB,GAJP;IAKpB,OAAO,AAAC,KAAED,OAAOK,IAAI,GAAG3B,aAAaC,OAAK,cAAWsB;AACvD;AAEA,SAASc,iBAAiB,KAKG;IALH,IAAA,EACxBf,MAAM,EACNrB,GAAG,EACHsB,KAAK,EACLC,OAAO,EACoB,GALH;IAMxB,sFAAsF;IACtF,MAAMI,SAAS;QAAC;QAAU;QAAW,OAAOL;QAAO,OAAQC,CAAAA,WAAW,MAAK;KAAG;IAC9E,MAAMc,eAAeV,OAAOI,IAAI,CAAC,OAAO;IACxC,OAAO,AAAC,KAAEV,OAAOK,IAAI,GAAGW,eAAetC,aAAaC;AACtD;AAEA,SAASsC,aAAa,KAAyB;IAAzB,IAAA,EAAEtC,GAAG,EAAoB,GAAzB;IACpB,MAAM,qBAGL,CAHK,IAAIuC,MACR,AAAC,qBAAkBvC,MAAI,gCACpB,4EAFC,qBAAA;eAAA;oBAAA;sBAAA;IAGN;AACF;AAEA,SAASwC,cAAc,KAKM;IALN,IAAA,EACrBnB,MAAM,EACNrB,GAAG,EACHsB,KAAK,EACLC,OAAO,EACoB,GALN;QAuFnBF;IAjFF,IAAIf,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;QACzC,MAAMC,gBAAgB,EAAE;QAExB,yDAAyD;QACzD,IAAI,CAAC1C,KAAK0C,cAAcC,IAAI,CAAC;QAC7B,IAAI,CAACrB,OAAOoB,cAAcC,IAAI,CAAC;QAE/B,IAAID,cAAcE,MAAM,GAAG,GAAG;YAC5B,MAAM,qBAML,CANK,IAAIL,MACR,AAAC,sCAAmCG,cAAcX,IAAI,CACpD,QACA,gGAA+Fc,KAAKC,SAAS,CAC7G;gBAAE9C;gBAAKsB;gBAAOC;YAAQ,KAJpB,qBAAA;uBAAA;4BAAA;8BAAA;YAMN;QACF;QAEA,IAAIvB,IAAI+C,UAAU,CAAC,OAAO;YACxB,MAAM,qBAEL,CAFK,IAAIR,MACR,AAAC,0BAAuBvC,MAAI,2GADxB,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIA,IAAI+C,UAAU,CAAC,QAAQ1B,OAAO2B,aAAa,EAAE;YAC/C,IACE1C,QAAQC,GAAG,CAACkC,QAAQ,KAAK,UACzB,gDAAgD;YAChDnC,QAAQC,GAAG,CAAC0C,YAAY,KAAK,QAC7B;gBACA,uEAAuE;gBACvE,MAAM,EACJC,aAAa,EACd,GAAGC,QAAQ;gBACZ,IAAI,CAACD,cAAc7B,OAAO2B,aAAa,EAAEhD,MAAM;oBAC7C,MAAM,qBAGL,CAHK,IAAIuC,MACR,AAAC,uBAAoBvC,MAAI,kGACtB,0FAFC,qBAAA;+BAAA;oCAAA;sCAAA;oBAGN;gBACF;YACF;QACF;QAEA,IAAI,CAACA,IAAI+C,UAAU,CAAC,QAAS1B,CAAAA,OAAO+B,OAAO,IAAI/B,OAAOgC,cAAc,AAAD,GAAI;YACrE,IAAIC;YACJ,IAAI;gBACFA,YAAY,IAAI7B,IAAIzB;YACtB,EAAE,OAAOuD,KAAK;gBACZC,QAAQC,KAAK,CAACF;gBACd,MAAM,qBAEL,CAFK,IAAIhB,MACR,AAAC,0BAAuBvC,MAAI,kIADxB,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IACEM,QAAQC,GAAG,CAACkC,QAAQ,KAAK,UACzB,gDAAgD;YAChDnC,QAAQC,GAAG,CAAC0C,YAAY,KAAK,QAC7B;gBACA,uEAAuE;gBACvE,MAAM,EACJS,cAAc,EACf,GAAGP,QAAQ;gBACZ,IAAI,CAACO,eAAerC,OAAO+B,OAAO,EAAE/B,OAAOgC,cAAc,EAAEC,YAAY;oBACrE,MAAM,qBAGL,CAHK,IAAIf,MACR,AAAC,uBAAoBvC,MAAI,kCAAiCsD,UAAUK,QAAQ,GAAC,gEAC1E,iFAFC,qBAAA;+BAAA;oCAAA;sCAAA;oBAGN;gBACF;YACF;QACF;QAEA,IAAIpC,WAAWF,OAAOuC,SAAS,IAAI,CAACvC,OAAOuC,SAAS,CAACC,QAAQ,CAACtC,UAAU;YACtE,MAAM,qBAGL,CAHK,IAAIgB,MACR,AAAC,2BAAwBhB,UAAQ,8FAC9B,sFAFC,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;IACF;IAEA,MAAMuC,IACJvC,aACAF,oBAAAA,OAAOuC,SAAS,qBAAhBvC,kBAAkB0C,MAAM,CAAC,CAACC,MAAMC,MAC9BC,KAAKC,GAAG,CAACF,MAAM7D,aAAa8D,KAAKC,GAAG,CAACH,OAAO5D,aAAa6D,MAAMD,UAEjE5D;IAEF,IAAI,CAACiB,OAAO+C,mBAAmB,IAAIpE,IAAIqE,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACC,QAAQ,CAAC,SAAS;QACxE,yDAAyD;QACzD,+CAA+C;QAC/C,OAAOtE;IACT;IAEA,OAAO,AAAGF,2BAA2BuB,OAAOK,IAAI,IAAE,UAAO6C,mBACvDvE,OACA,QAAKsB,QAAM,QAAKwC;AACpB;AAEA,MAAMU,UAAU,IAAI5D,IAGlB;IACA;QAAC;QAAW4B;KAAc;IAC1B;QAAC;QAASpB;KAAY;IACtB;QAAC;QAAcgB;KAAiB;IAChC;QAAC;QAAUD;KAAa;IACxB;QAAC;QAAUG;KAAa;CACzB;AAED,MAAMmC,sBAAsB;IAC1B;IACA;IACA;IACA;IACAtD;CACD;AA+BD,SAASuD,gBACP1E,GAAoC;IAEpC,OAAO,AAACA,IAAsB2E,OAAO,KAAKxD;AAC5C;AAEA,SAASyD,kBACP5E,GAAoC;IAEpC,OAAO,AAACA,IAAwBA,GAAG,KAAKmB;AAC1C;AAEA,SAAS0D,eAAe7E,GAA0B;IAChD,OACE,OAAOA,QAAQ,YACd0E,CAAAA,gBAAgB1E,QACf4E,kBAAkB5E,IAAmB;AAE3C;AA8CA,SAAS8E,UACP,KAAsC,EACtCxD,KAAyB,EACzByD,MAAmB,EACnBC,KAAyB;IAHzB,IAAA,EAAEC,WAAW,EAAEC,QAAQ,EAAe,GAAtC;IAKA,IAAIF,SAAUD,CAAAA,WAAW,UAAUA,WAAW,YAAW,GAAI;QAC3D,yDAAyD;QACzD,MAAMI,kBAAkB;QACxB,MAAMC,eAAe,EAAE;QACvB,IAAK,IAAIC,OAAQA,QAAQF,gBAAgBG,IAAI,CAACN,QAASK,MAAO;YAC5DD,aAAazC,IAAI,CAAC4C,SAASF,KAAK,CAAC,EAAE;QACrC;QACA,IAAID,aAAaxC,MAAM,EAAE;YACvB,MAAM4C,gBAAgBtB,KAAKuB,GAAG,IAAIL,gBAAgB;YAClD,OAAO;gBACLM,QAAQR,SAASS,MAAM,CAAC,CAACC,IAAMA,KAAKX,WAAW,CAAC,EAAE,GAAGO;gBACrDK,MAAM;YACR;QACF;QACA,OAAO;YAAEH,QAAQR;YAAUW,MAAM;QAAI;IACvC;IACA,IACE,OAAOvE,UAAU,YACjByD,WAAW,UACXA,WAAW,cACX;QACA,OAAO;YAAEW,QAAQT;YAAaY,MAAM;QAAI;IAC1C;IAEA,MAAMH,SAAS;WACV,IAAIhF,IACL,uEAAuE;QACvE,qEAAqE;QACrE,kEAAkE;QAClE,oEAAoE;QACpE,uEAAuE;QACvE,sEAAsE;QACtE,uCAAuC;QACvC,qIAAqI;QACrI;YAACY;YAAOA,QAAQ,EAAE,aAAa;SAAG,CAACwE,GAAG,CACpC,CAACC,IAAMb,SAASc,IAAI,CAAC,CAACC,IAAMA,KAAKF,MAAMb,QAAQ,CAACA,SAAStC,MAAM,GAAG,EAAE;KAGzE;IACD,OAAO;QAAE8C;QAAQG,MAAM;IAAI;AAC7B;AAmBA,SAASK,iBAAiB,KASR;IATQ,IAAA,EACxB7E,MAAM,EACNrB,GAAG,EACHmG,WAAW,EACXpB,MAAM,EACNzD,KAAK,EACLC,OAAO,EACPyD,KAAK,EACLoB,MAAM,EACU,GATQ;IAUxB,IAAID,aAAa;QACf,OAAO;YAAEnG;YAAKqG,QAAQlF;YAAW6D,OAAO7D;QAAU;IACpD;IAEA,MAAM,EAAEuE,MAAM,EAAEG,IAAI,EAAE,GAAGf,UAAUzD,QAAQC,OAAOyD,QAAQC;IAC1D,MAAMsB,OAAOZ,OAAO9C,MAAM,GAAG;IAE7B,OAAO;QACLoC,OAAO,CAACA,SAASa,SAAS,MAAM,UAAUb;QAC1CqB,QAAQX,OACLI,GAAG,CACF,CAACC,GAAGQ,IACF,AAAGH,OAAO;gBAAE/E;gBAAQrB;gBAAKuB;gBAASD,OAAOyE;YAAE,KAAG,MAC5CF,CAAAA,SAAS,MAAME,IAAIQ,IAAI,CAAA,IACtBV,MAEN9D,IAAI,CAAC;QAER,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtD/B,KAAKoG,OAAO;YAAE/E;YAAQrB;YAAKuB;YAASD,OAAOoE,MAAM,CAACY,KAAK;QAAC;IAC1D;AACF;AAEA,SAASE,OAAOC,CAAU;IACxB,IAAI,OAAOA,MAAM,UAAU;QACzB,OAAOA;IACT;IACA,IAAI,OAAOA,MAAM,UAAU;QACzB,OAAOlB,SAASkB,GAAG;IACrB;IACA,OAAOtF;AACT;AAEA,SAASuF,mBAAmBC,WAAuC;QAC/CA;IAAlB,MAAMC,YAAYD,EAAAA,sBAAAA,YAAYtF,MAAM,qBAAlBsF,oBAAoBP,MAAM,KAAI;IAChD,MAAMS,OAAOrC,QAAQxC,GAAG,CAAC4E;IACzB,IAAIC,MAAM;QACR,OAAOA,KAAKF;IACd;IACA,MAAM,qBAIL,CAJK,IAAIpE,MACR,AAAC,2DAAwD7C,cAAcqC,IAAI,CACzE,QACA,iBAAc6E,YAHZ,qBAAA;eAAA;oBAAA;sBAAA;IAIN;AACF;AAEA,0EAA0E;AAC1E,iDAAiD;AACjD,SAASE,cACPC,GAA2B,EAC3B/G,GAAW,EACX+E,MAAmB,EACnBiC,WAA6B,EAC7BC,oBAA2E,EAC3EC,eAAqC;IAErC,IAAI,CAACH,OAAOA,IAAI/G,GAAG,KAAKc,gBAAgBiG,GAAG,CAAC,kBAAkB,KAAK/G,KAAK;QACtE;IACF;IACA+G,GAAG,CAAC,kBAAkB,GAAG/G;IACzB,MAAMiG,IAAI,YAAYc,MAAMA,IAAII,MAAM,KAAKC,QAAQC,OAAO;IAC1DpB,EAAEqB,KAAK,CAAC,KAAO,GAAGC,IAAI,CAAC;QACrB,IAAI,CAACR,IAAIS,UAAU,EAAE;YACnB,wCAAwC;YACxC,uBAAuB;YACvB,sCAAsC;YACtC,sBAAsB;YACtB,uBAAuB;YACvB;QACF;QACA/G,gBAAgBgH,GAAG,CAACzH;QACpB,IAAIgH,gBAAgB,QAAQ;YAC1BE,gBAAgB;QAClB;QACA,IAAID,wCAAAA,qBAAsBS,OAAO,EAAE;YACjC,MAAM,EAAEC,YAAY,EAAEC,aAAa,EAAE,GAAGb;YACxC,mDAAmD;YACnD,sDAAsD;YACtDE,qBAAqBS,OAAO,CAAC;gBAAEC;gBAAcC;YAAc;QAC7D;QACA,IAAItH,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;gBACrCsE;YAAJ,KAAIA,qBAAAA,IAAIc,aAAa,qBAAjBd,mBAAmBc,aAAa,EAAE;gBACpC,MAAMC,SAASC,iBAAiBhB,IAAIc,aAAa,CAACA,aAAa;gBAC/D,IAAI,CAACC,OAAOE,QAAQ,EAAE;gBACpB,sHAAsH;gBACxH,OAAO,IAAIjD,WAAW,gBAAgB+C,OAAOG,OAAO,KAAK,QAAQ;oBAC/DpI,SACE,AAAC,qBAAkBG,MAAI;gBAE3B,OAAO,IACL+E,WAAW,UACX+C,OAAOE,QAAQ,KAAK,cACpBF,OAAOE,QAAQ,KAAK,WACpBF,OAAOE,QAAQ,KAAK,YACpB;oBACAnI,SACE,AAAC,qBAAkBG,MAAI,6DAA0D8H,OAAOE,QAAQ,GAAC;gBAErG;YACF;QACF;IACF;AACF;AAEA,MAAME,eAAe;QAAC,EACpBC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVvD,MAAM,EACNwD,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,MAAM,EACN1B,WAAW,EACX2B,OAAO,EACPC,SAAS,EACTvH,MAAM,EACN8E,WAAW,EACXC,MAAM,EACNa,oBAAoB,EACpBC,eAAe,EACf2B,eAAe,EACfC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,aAAa,EACb,GAAGC,MACe;IAClBP,UAAUD,SAAS,SAASC;IAC5B,qBACE;;0BACE,KAAC5B;gBACE,GAAGmC,IAAI;gBACP,GAAGf,aAAa;gBACjBgB,UAAS;gBACTC,aAAWrE;gBACXwD,WAAWA;gBACXc,OAAO;oBAAE,GAAGb,QAAQ;oBAAE,GAAGC,SAAS;gBAAC;gBACnCa,KAAKnK,YACH,CAAC4H;oBACC,IAAIzG,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;wBACzC,IAAIsE,OAAO,CAAC6B,WAAW;4BACrBpF,QAAQC,KAAK,CAAE,6CAA4CsD;wBAC7D;oBACF;oBACA8B,gBAAgB9B;oBAChB,IAAIA,uBAAAA,IAAKwC,QAAQ,EAAE;wBACjBzC,cACEC,KACA6B,WACA7D,QACAiC,aACAC,sBACAC;oBAEJ;gBACF,GACA;oBACE2B;oBACAD;oBACA7D;oBACAiC;oBACAC;oBACAC;iBACD;gBAEH4B,QAAQ,CAACU;oBACP,MAAMzC,MAAMyC,MAAMC,aAAa;oBAC/B3C,cACEC,KACA6B,WACA7D,QACAiC,aACAC,sBACAC;oBAEF,IAAI4B,QAAQ;wBACVA,OAAOU;oBACT;gBACF;gBACAT,SAAS,CAACS;oBACR,IAAIxC,gBAAgB,QAAQ;wBAC1B,2EAA2E;wBAC3EE,gBAAgB;oBAClB;oBACA,IAAI6B,SAAS;wBACXA,QAAQS;oBACV;gBACF;;YAEAd,CAAAA,UAAU1B,gBAAgB,MAAK,mBAC/B,KAAC0C;0BACC,cAAA,KAAC3C;oBACE,GAAGmC,IAAI;oBACR,kDAAkD;oBAClDP,SAASA;oBACTQ,UAAS;oBACTC,aAAWrE;oBACXsE,OAAOb;oBACPD,WAAWA;oBAIV,GAAGrC,iBAAiB;wBACnB7E;wBACArB,KAAK4I;wBACLzC;wBACApB;wBACAzD,OAAO+G;wBACP9G,SAAS+G;wBACTtD,OAAOiE;wBACP7C;oBACF,EAAE;;;;;AAMd;AAEA,eAAe,SAASuD,MAAM,KAmBjB;IAnBiB,IAAA,EAC5B3J,GAAG,EACHgF,KAAK,EACLmB,cAAc,KAAK,EACnByD,WAAW,KAAK,EAChBjB,OAAO,EACPkB,WAAW,IAAI,EACfC,YAAY,EACZvB,SAAS,EACThH,OAAO,EACPD,KAAK,EACLyI,MAAM,EACNV,KAAK,EACLW,SAAS,EACTC,cAAc,EACdC,iBAAiB,EACjBlD,cAAc,OAAO,EACrBmD,WAAW,EACX,GAAGC,KACQ,GAnBiB;IAoB5B,MAAMC,gBAAgBjL,WAAWQ;IACjC,MAAMyB,SAAsBhC,QAAQ;YAIhBiL;QAHlB,MAAMA,IAAIjK,aAAagK,iBAAiB5K;QACxC,MAAMyF,WAAW;eAAIoF,EAAErF,WAAW;eAAKqF,EAAEC,UAAU;SAAC,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAMzF,cAAcqF,EAAErF,WAAW,CAACuF,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrD,MAAM9G,aAAY0G,eAAAA,EAAE1G,SAAS,qBAAX0G,aAAaE,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QAClD,OAAO;YAAE,GAAGJ,CAAC;YAAEpF;YAAUD;YAAarB;QAAU;IAClD,GAAG;QAACyG;KAAc;IAElB,IAAInB,OAA4BkB;IAChC,IAAIrF,SAAmCC,QAAQ,eAAe;IAC9D,IAAI,YAAYkE,MAAM;QACpB,qDAAqD;QACrD,IAAIA,KAAKnE,MAAM,EAAEA,SAASmE,KAAKnE,MAAM;QAErC,+CAA+C;QAC/C,OAAOmE,KAAKnE,MAAM;IACpB;IAEA,IAAIqB,SAAgCM;IACpC,IAAI,YAAYwC,MAAM;QACpB,IAAIA,KAAK9C,MAAM,EAAE;YACf,MAAMuE,oBAAoBzB,KAAK9C,MAAM;YACrCA,SAAS,CAACwE;gBACR,MAAM,EAAEvJ,QAAQwJ,CAAC,EAAE,GAAGC,MAAM,GAAGF;gBAC/B,gDAAgD;gBAChD,2CAA2C;gBAC3C,OAAOD,kBAAkBG;YAC3B;QACF;QACA,8CAA8C;QAC9C,OAAO5B,KAAK9C,MAAM;IACpB;IAEA,IAAI2E,YAAY;IAChB,IAAIlG,eAAe7E,MAAM;QACvB,MAAMgL,kBAAkBtG,gBAAgB1E,OAAOA,IAAI2E,OAAO,GAAG3E;QAE7D,IAAI,CAACgL,gBAAgBhL,GAAG,EAAE;YACxB,MAAM,qBAIL,CAJK,IAAIuC,MACR,AAAC,gJAA6IM,KAAKC,SAAS,CAC1JkI,mBAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;QACAb,cAAcA,eAAea,gBAAgBb,WAAW;QACxDY,YAAYC,gBAAgBhL,GAAG;QAC/B,IAAI,CAAC+E,UAAUA,WAAW,QAAQ;YAChCgF,SAASA,UAAUiB,gBAAgBjB,MAAM;YACzCzI,QAAQA,SAAS0J,gBAAgB1J,KAAK;YACtC,IAAI,CAAC0J,gBAAgBjB,MAAM,IAAI,CAACiB,gBAAgB1J,KAAK,EAAE;gBACrD,MAAM,qBAIL,CAJK,IAAIiB,MACR,AAAC,6JAA0JM,KAAKC,SAAS,CACvKkI,mBAFE,qBAAA;2BAAA;gCAAA;kCAAA;gBAIN;YACF;QACF;IACF;IACAhL,MAAM,OAAOA,QAAQ,WAAWA,MAAM+K;IAEtC,IAAIrC,SACF,CAACkB,YAAajB,CAAAA,YAAY,UAAU,OAAOA,YAAY,WAAU;IACnE,IAAI3I,IAAI+C,UAAU,CAAC,YAAY/C,IAAI+C,UAAU,CAAC,UAAU;QACtD,uEAAuE;QACvEoD,cAAc;QACduC,SAAS;IACX;IACA,IAAI,OAAO3H,WAAW,eAAeN,gBAAgBwK,GAAG,CAACjL,MAAM;QAC7D0I,SAAS;IACX;IACA,IAAIrH,OAAO8E,WAAW,EAAE;QACtBA,cAAc;IAChB;IAEA,MAAM,CAAC+E,cAAchE,gBAAgB,GAAG5H,SAAS;IACjD,MAAM,CAACuJ,iBAAiBsC,eAAeC,iBAAiB,GACtDzL,gBAAkC;QAChC0L,SAASxB;QACTyB,YAAYxB,gBAAgB;QAC5ByB,UAAU,CAAC7C;IACb;IACF,MAAMM,YAAY,CAACN,UAAUyC;IAE7B,MAAMK,eAAuD;QAC3DC,WAAW;QACXxD,SAAS;QACTyD,UAAU;QACVpK,OAAO;QACPyI,QAAQ;QACR4B,YAAY;QACZC,SAAS;QACTC,QAAQ;QACRC,QAAQ;QACRC,SAAS;IACX;IACA,MAAMC,aAAqD;QACzDP,WAAW;QACXxD,SAAS;QACT3G,OAAO;QACPyI,QAAQ;QACR4B,YAAY;QACZC,SAAS;QACTC,QAAQ;QACRC,QAAQ;QACRC,SAAS;IACX;IACA,IAAIE,WAAW;IACf,IAAIC;IACJ,MAAMC,cAA+B;QACnCnE,UAAU;QACVoE,KAAK;QACLC,MAAM;QACNC,QAAQ;QACRC,OAAO;QAEPd,WAAW;QACXM,SAAS;QACTF,QAAQ;QACRC,QAAQ;QAER7D,SAAS;QACT3G,OAAO;QACPyI,QAAQ;QACRyC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,WAAW;QAEX3C;QACAC;IACF;IAEA,IAAI5B,WAAW7B,OAAOlF;IACtB,IAAI8G,YAAY5B,OAAOuD;IACvB,MAAMzB,aAAa9B,OAAOjF;IAE1B,IAAIjB,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;QACzC,IAAI,CAACzC,KAAK;YACR,iDAAiD;YACjD,+CAA+C;YAC/C,2CAA2C;YAC3CqI,WAAWA,YAAY;YACvBD,YAAYA,aAAa;YACzBjC,cAAc;QAChB,OAAO;YACL,IAAI,CAAC1B,oBAAoBZ,QAAQ,CAACkB,SAAS;gBACzC,MAAM,qBAIL,CAJK,IAAIxC,MACR,AAAC,qBAAkBvC,MAAI,gDAA6C+E,SAAO,wBAAqBN,oBAAoBqB,GAAG,CACrH8G,QACA7K,IAAI,CAAC,OAAK,MAHR,qBAAA;2BAAA;gCAAA;kCAAA;gBAIN;YACF;YAEA,IACE,AAAC,OAAOsG,aAAa,eAAewE,MAAMxE,aACzC,OAAOD,cAAc,eAAeyE,MAAMzE,YAC3C;gBACA,MAAM,qBAEL,CAFK,IAAI7F,MACR,AAAC,qBAAkBvC,MAAI,gFADnB,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,IAAI+E,WAAW,UAAWzD,CAAAA,SAASyI,MAAK,GAAI;gBAC1ClK,SACE,AAAC,qBAAkBG,MAAI;YAE3B;YACA,IAAI,CAACkB,qBAAqB2C,QAAQ,CAAC8E,UAAU;gBAC3C,MAAM,qBAIL,CAJK,IAAIpG,MACR,AAAC,qBAAkBvC,MAAI,iDAA8C2I,UAAQ,wBAAqBzH,qBAAqB4E,GAAG,CACxH8G,QACA7K,IAAI,CAAC,OAAK,MAHR,qBAAA;2BAAA;gCAAA;kCAAA;gBAIN;YACF;YACA,IAAI6H,YAAYjB,YAAY,QAAQ;gBAClC,MAAM,qBAEL,CAFK,IAAIpG,MACR,AAAC,qBAAkBvC,MAAI,sFADnB,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,IAAIgF,SAASD,WAAW,UAAUA,WAAW,cAAc;gBACzDlF,SACE,AAAC,qBAAkBG,MAAI;YAE3B;YACA,IAAIgH,gBAAgB,QAAQ;gBAC1B,IAAIjC,WAAW,UAAU,AAACsD,CAAAA,YAAY,CAAA,IAAMD,CAAAA,aAAa,CAAA,IAAK,MAAM;oBAClEvI,SACE,AAAC,qBAAkBG,MAAI;gBAE3B;gBACA,IAAI,CAACmK,aAAa;oBAChB,MAAM2C,iBAAiB;wBAAC;wBAAQ;wBAAO;wBAAQ;qBAAO,CAAC,iCAAiC;;oBAExF,MAAM,qBASL,CATK,IAAIvK,MACR,AAAC,qBAAkBvC,MAAI,mUAGgE8M,eAAe/K,IAAI,CACxG,OACA,mMANE,qBAAA;+BAAA;oCAAA;sCAAA;oBASN;gBACF;YACF;YACA,IAAI,SAASmH,MAAM;gBACjBrJ,SACE,AAAC,qBAAkBG,MAAI;YAE3B;YAEA,IAAI,CAACmG,eAAeC,WAAWM,oBAAoB;gBACjD,MAAMqG,SAAS3G,OAAO;oBACpB/E;oBACArB;oBACAsB,OAAO+G,YAAY;oBACnB9G,SAAS+G,cAAc;gBACzB;gBACA,IAAI9G;gBACJ,IAAI;oBACFA,MAAM,IAAIC,IAAIsL;gBAChB,EAAE,OAAOxJ,KAAK,CAAC;gBACf,IAAIwJ,WAAW/M,OAAQwB,OAAOA,IAAIwL,QAAQ,KAAKhN,OAAO,CAACwB,IAAIyL,MAAM,EAAG;oBAClEpN,SACE,AAAC,qBAAkBG,MAAI,4HACpB;gBAEP;YACF;YAEA,IAAIqJ,OAAO;gBACT,IAAI6D,oBAAoBC,OAAOC,IAAI,CAAC/D,OAAO1D,MAAM,CAC/C,CAAC0H,MAAQA,OAAOlB;gBAElB,IAAIe,kBAAkBtK,MAAM,EAAE;oBAC5B/C,SACE,AAAC,oBAAiBG,MAAI,iGAA8FkN,kBAAkBnL,IAAI,CACxI;gBAGN;YACF;YAEA,IACE,OAAOhB,WAAW,eAClB,CAACF,gBACDE,OAAOuM,mBAAmB,EAC1B;gBACAzM,eAAe,IAAIyM,oBAAoB,CAACC;oBACtC,KAAK,MAAMC,SAASD,UAAUE,UAAU,GAAI;4BAE3BD;wBADf,0EAA0E;wBAC1E,MAAME,SAASF,CAAAA,0BAAAA,iBAAAA,MAAOG,OAAO,qBAAdH,eAAgBxN,GAAG,KAAI;wBACtC,MAAM4N,WAAWjN,QAAQqB,GAAG,CAAC0L;wBAC7B,IACEE,YACA,CAACA,SAAShE,QAAQ,IAClBgE,SAAS5G,WAAW,KAAK,UACzB,CAAC4G,SAAS5N,GAAG,CAAC+C,UAAU,CAAC,YACzB,CAAC6K,SAAS5N,GAAG,CAAC+C,UAAU,CAAC,UACzB;4BACA,iDAAiD;4BACjDlD,SACE,AAAC,qBAAkB+N,SAAS5N,GAAG,GAAC,8HAC7B;wBAEP;oBACF;gBACF;gBACA,IAAI;oBACFa,aAAagN,OAAO,CAAC;wBACnBC,MAAM;wBACNC,UAAU;oBACZ;gBACF,EAAE,OAAOxK,KAAK;oBACZ,oCAAoC;oBACpCC,QAAQC,KAAK,CAACF;gBAChB;YACF;QACF;IACF;IACA,MAAMiF,WAAW2E,OAAOa,MAAM,CAAC,CAAC,GAAG3E,OAAO8C;IAC1C,MAAM1D,YACJzB,gBAAgB,UAAU,CAACkE,eACvB;QACE+C,gBAAgBjE,aAAa;QAC7BkE,oBAAoBjE,kBAAkB;QACtCtE,QAAQ;QACRwI,iBAAiB,AAAC,UAAOhE,cAAY;IACvC,IACA,CAAC;IACP,IAAIpF,WAAW,QAAQ;QACrB,sCAAsC;QACtCyG,aAAavD,OAAO,GAAG;QACvBuD,aAAaxD,QAAQ,GAAG;QACxBwD,aAAaY,GAAG,GAAG;QACnBZ,aAAaa,IAAI,GAAG;QACpBb,aAAac,MAAM,GAAG;QACtBd,aAAae,KAAK,GAAG;IACvB,OAAO,IACL,OAAOlE,aAAa,eACpB,OAAOD,cAAc,aACrB;QACA,iDAAiD;QACjD,MAAMgG,WAAWhG,YAAYC;QAC7B,MAAMgG,aAAaxB,MAAMuB,YAAY,SAAS,AAAC,KAAEA,WAAW,MAAI;QAChE,IAAIrJ,WAAW,cAAc;YAC3B,qEAAqE;YACrEyG,aAAavD,OAAO,GAAG;YACvBuD,aAAaxD,QAAQ,GAAG;YACxBiE,WAAW;YACXD,WAAWqC,UAAU,GAAGA;QAC1B,OAAO,IAAItJ,WAAW,aAAa;YACjC,oEAAoE;YACpEyG,aAAavD,OAAO,GAAG;YACvBuD,aAAaxD,QAAQ,GAAG;YACxBwD,aAAaiB,QAAQ,GAAG;YACxBR,WAAW;YACXD,WAAWS,QAAQ,GAAG;YACtBP,cAAc,AAAC,uGAAoG7D,WAAS,qBAAkBD,YAAU;QAC1J,OAAO,IAAIrD,WAAW,SAAS;YAC7B,gEAAgE;YAChEyG,aAAavD,OAAO,GAAG;YACvBuD,aAAaxD,QAAQ,GAAG;YACxBwD,aAAalK,KAAK,GAAG+G;YACrBmD,aAAazB,MAAM,GAAG3B;QACxB;IACF,OAAO;QACL,wBAAwB;QACxB,IAAI9H,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;YACzC,MAAM,qBAEL,CAFK,IAAIF,MACR,AAAC,qBAAkBvC,MAAI,8EADnB,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,IAAImI,gBAAmC;QACrCnI,KAAKc;QACLuF,QAAQlF;QACR6D,OAAO7D;IACT;IAEA,IAAI6H,WAAW;QACbb,gBAAgBjC,iBAAiB;YAC/B7E;YACArB;YACAmG;YACApB;YACAzD,OAAO+G;YACP9G,SAAS+G;YACTtD;YACAoB;QACF;IACF;IAEA,IAAIwC,YAAoB5I;IAExB,IAAIM,QAAQC,GAAG,CAACkC,QAAQ,KAAK,cAAc;QACzC,IAAI,OAAO1B,WAAW,aAAa;YACjC,IAAIuN;YACJ,IAAI;gBACFA,UAAU,IAAI7M,IAAI0G,cAAcnI,GAAG;YACrC,EAAE,OAAOuO,GAAG;gBACVD,UAAU,IAAI7M,IAAI0G,cAAcnI,GAAG,EAAEe,OAAOyN,QAAQ,CAACtM,IAAI;YAC3D;YACAvB,QAAQkB,GAAG,CAACyM,QAAQpM,IAAI,EAAE;gBAAElC;gBAAK4J;gBAAU5C;YAAY;QACzD;IACF;IAEA,MAAMyH,YAKUvO,gBACZiB,YACA;QACEuN,aAAavG,cAAc9B,MAAM;QACjCkE,YAAYpC,cAAcnD,KAAK;QAC/B2J,aAAazF,KAAKyF,WAAW;QAC7BC,gBAAgB1F,KAAK0F,cAAc;IACrC;IAEJ,MAAMC,kBACJ,OAAO9N,WAAW,cAAc/B,MAAME,SAAS,GAAGF,MAAM6P,eAAe;IACzE,MAAM5H,uBAAuBhI,OAAOiL;IAEpC,MAAM4E,mBAAmB7P,OAA8Be;IACvDd,UAAU;QACR+H,qBAAqBS,OAAO,GAAGwC;IACjC,GAAG;QAACA;KAAkB;IAEtB2E,gBAAgB;QACd,IAAIC,iBAAiBpH,OAAO,KAAK1H,KAAK;YACpCoL;YACA0D,iBAAiBpH,OAAO,GAAG1H;QAC7B;IACF,GAAG;QAACoL;QAAkBpL;KAAI;IAE1B,MAAM+O,iBAAiB;QACrBrG;QACAP;QACAC;QACAC;QACAC;QACAvD;QACAwD;QACAC;QACAC;QACAE;QACAtH;QACA8E;QACAa;QACAZ;QACAwC;QACA3B;QACAC;QACA2B;QACAG;QACAC,eAAejE;QACf,GAAGkE,IAAI;IACT;IACA,qBACE;;0BACE,MAAC8F;gBAAK3F,OAAOmC;;oBACVS,yBACC,KAAC+C;wBAAK3F,OAAO2C;kCACVE,4BACC,KAACnF;4BACCsC,OAAO;gCACLpB,SAAS;gCACTwE,UAAU;gCACVnL,OAAO;gCACPyI,QAAQ;gCACR4B,YAAY;gCACZC,SAAS;gCACTC,QAAQ;gCACRC,QAAQ;gCACRC,SAAS;4BACX;4BACAkD,KAAI;4BACJC,eAAa;4BACblP,KAAKkM;6BAEL;yBAEJ;kCACJ,KAAChE;wBAAc,GAAG6G,cAAc;;;;YAEjC,CAAC7O,iBAAiB0J,WACjB,sEAAsE;YACtE,qEAAqE;YACrE,6DAA6D;YAC7D,EAAE;YACF,8EAA8E;0BAC9E,KAACpK;0BACC,cAAA,KAAC2P;oBAOCC,KAAI;oBACJC,IAAG;oBACHnN,MAAMiG,cAAc9B,MAAM,GAAGlF,YAAYgH,cAAcnI,GAAG;oBACzD,GAAGyO,SAAS;mBARX,YACAtG,cAAcnI,GAAG,GACjBmI,cAAc9B,MAAM,GACpB8B,cAAcnD,KAAK;iBAQvB;;;AAGV"}