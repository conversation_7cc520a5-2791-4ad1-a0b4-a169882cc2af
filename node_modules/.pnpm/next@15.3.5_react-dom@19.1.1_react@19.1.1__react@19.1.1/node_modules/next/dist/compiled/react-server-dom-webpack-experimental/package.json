{"name": "react-server-dom-webpack-experimental-builtin", "main": "index.js", "exports": {".": "./index.js", "./plugin": "./plugin.js", "./client": {"workerd": "./client.edge.js", "deno": "./client.edge.js", "worker": "./client.edge.js", "node": {"webpack": "./client.node.js", "default": "./client.node.unbundled.js"}, "edge-light": "./client.edge.js", "browser": "./client.browser.js", "default": "./client.browser.js"}, "./client.browser": "./client.browser.js", "./client.edge": "./client.edge.js", "./client.node": "./client.node.js", "./client.node.unbundled": "./client.node.unbundled.js", "./server": {"react-server": {"workerd": "./server.edge.js", "deno": "./server.browser.js", "node": {"webpack": "./server.node.js", "default": "./server.node.unbundled.js"}, "edge-light": "./server.edge.js", "browser": "./server.browser.js"}, "default": "./server.js"}, "./server.browser": "./server.browser.js", "./server.edge": "./server.edge.js", "./server.node": "./server.node.js", "./server.node.unbundled": "./server.node.unbundled.js", "./static": {"react-server": {"workerd": "./static.edge.js", "deno": "./static.browser.js", "node": {"webpack": "./static.node.js", "default": "./static.node.unbundled.js"}, "edge-light": "./static.edge.js", "browser": "./static.browser.js"}, "default": "./static.js"}, "./static.browser": "./static.browser.js", "./static.edge": "./static.edge.js", "./static.node": "./static.node.js", "./static.node.unbundled": "./static.node.unbundled.js", "./node-loader": "./esm/react-server-dom-webpack-node-loader.production.js", "./node-register": "./node-register.js", "./package.json": "./package.json"}, "dependencies": {"acorn-loose": "^8.3.0", "neo-async": "^2.6.1", "webpack-sources": "^3.2.0"}, "peerDependencies": {"react": "0.0.0-experimental-3fbfb9ba-20250409", "react-dom": "0.0.0-experimental-3fbfb9ba-20250409", "webpack": "^5.59.0"}}