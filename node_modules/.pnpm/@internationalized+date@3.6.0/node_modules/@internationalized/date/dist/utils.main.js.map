{"mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC;AAQM,SAAS,0CAAI,MAAc,EAAE,SAAiB;IACnD,OAAO,SAAS,YAAY,KAAK,KAAK,CAAC,SAAS;AAClD;AAEO,SAAS,0CAAK,IAAkB;IACrC,IAAI,KAAK,GAAG,EACV,OAAO,IAAI,CAAA,GAAA,sCAAW,EAAE,KAAK,QAAQ,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG;SAEhF,OAAO,IAAI,CAAA,GAAA,sCAAW,EAAE,KAAK,QAAQ,EAAE,KAAK,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG;AAE1E;AAEO,SAAS,0CAAa,IAAsB;IACjD,IAAI,KAAK,GAAG,EACV,OAAO,IAAI,CAAA,GAAA,0CAAe,EAAE,KAAK,QAAQ,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,WAAW;SAE3I,OAAO,IAAI,CAAA,GAAA,0CAAe,EAAE,KAAK,QAAQ,EAAE,KAAK,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM;AAEnH", "sources": ["packages/@internationalized/date/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CalendarDate, CalendarDateTime} from './CalendarDate';\n\nexport type Mutable<T> = {\n  -readonly[P in keyof T]: T[P]\n};\n\nexport function mod(amount: number, numerator: number): number {\n  return amount - numerator * Math.floor(amount / numerator);\n}\n\nexport function copy(date: CalendarDate): Mutable<CalendarDate> {\n  if (date.era) {\n    return new CalendarDate(date.calendar, date.era, date.year, date.month, date.day);\n  } else {\n    return new CalendarDate(date.calendar, date.year, date.month, date.day);\n  }\n}\n\nexport function copyDateTime(date: CalendarDateTime): Mutable<CalendarDateTime> {\n  if (date.era) {\n    return new CalendarDateTime(date.calendar, date.era, date.year, date.month, date.day, date.hour, date.minute, date.second, date.millisecond);\n  } else {\n    return new CalendarDateTime(date.calendar, date.year, date.month, date.day, date.hour, date.minute, date.second);\n  }\n}\n"], "names": [], "version": 3, "file": "utils.main.js.map"}