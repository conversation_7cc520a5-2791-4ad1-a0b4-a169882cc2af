{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;;;AAMhG,mBAAmB;AACnB,MAAM,yCAAmB;AAEzB,gEAAgE;AAChE,MAAM,0CAAoB;AAOnB,MAAM,kDAAuB,CAAA,GAAA,2CAAgB;IAGlD,cAAc,EAAU,EAAgB;QACtC,gCAAgC;QAChC,IAAI,OAAO,KAAK,CAAC,cAAc;QAE/B,mBAAmB;QACnB,IAAI,aAAa,KAAK,IAAI,GAAG;QAE7B,iDAAiD;QACjD,IAAI,OAAO,KAAK,CAAA,GAAA,8CAAmB,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI,EAAE,GAAG;QAE7D,IAAI;QACJ,IAAI,OAAO,yCAAmB;YAC5B,gDAAgD;YAChD;YAEA,uDAAuD;YACvD,YAAY,CAAA,GAAA,oCAAS,EAAE,KAAK,IAAI,GAAG,KAAK,KAAK;YAC7C,QAAQ,YAAa,MAAW,KAAU;QAC5C,OAAO;YACL,8BAA8B;YAC9B,YAAY,CAAA,GAAA,oCAAS,EAAE,KAAK,IAAI,IAAI,KAAK;YACzC,QAAQ;QACV;QAEA,IAAI;QACJ,IAAI;QACJ,IAAI,OAAO,WAAW;YACpB,cAAc;YACd,YAAY,OAAO;QACrB,OAAO;YACL,IAAI,OAAO,OAAO;YAClB,IAAI,OAAQ,KAAS;gBACnB,cAAc,KAAK,KAAK,CAAC,OAAO,MAAM;gBACtC,YAAY,AAAC,OAAO,KAAM;YAC5B,OAAO;gBACL,QAAQ;gBACR,cAAc,KAAK,KAAK,CAAC,OAAO,MAAM;gBACtC,YAAY,AAAC,OAAO,KAAM;YAC5B;QACF;QAEA,OAAO,IAAI,CAAA,GAAA,sCAAW,EAAE,IAAI,EAAE,YAAY,aAAa;IACzD;IAEA,YAAY,IAAqB,EAAE;QACjC,IAAI,eAAe,KAAK,IAAI,GAAG;QAC/B,IAAI,CAAC,KAAK,KAAK,GAAG,CAAA,GAAA,0CAAe,EAAE;QAEnC,IAAI;QACJ,IAAI;QACJ,IAAI,CAAA,GAAA,oCAAS,EAAE,OAAO;YACpB,YAAY;YACZ,KAAK,CAAA,GAAA,8CAAmB,EAAE,KAAK,MAAM,GAAG;QAC1C,OAAO;YACL,YAAY;YACZ,KAAK,CAAA,GAAA,8CAAmB,EAAE,KAAK,MAAM,GAAG;QAC1C;QAEA,IAAI,KAAK,KAAK,KAAK,GACjB,OAAO,KAAK,KAAK,GAAG,GAAG;QAGzB,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,GAAG,KAAK;QAEhD,IAAI,KAAK,KAAK,IAAI,GAChB,MAAM,AAAC,CAAA,KAAK,KAAK,GAAG,CAAA,IAAK;QAG3B,MAAM,KAAK,GAAG,GAAG;QACjB,OAAO;IACT;IAEA,eAAe,IAAqB,EAAU;QAC5C,IAAI,KAAK,KAAK,KAAK,KAAK,CAAA,GAAA,oCAAS,EAAE,KAAK,IAAI,GAAG,yCAC7C,OAAO;QAGT,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,GACnC,OAAO;QAGT,OAAO;IACT;IAEA,gBAAwB;QACtB,6CAA6C;QAC7C,6CAA6C;QAC7C,OAAO;IACT;IAEA,UAAU;QACR,OAAO;YAAC;SAAO;IACjB;IAEA,cAAc,CAAC;;6BAhGf,aAAa;;AAiGf", "sources": ["packages/@internationalized/date/src/calendars/IndianCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {fromExtendedYear, GregorianCalendar, gregorianToJulianDay, isLeapYear} from './GregorianCalendar';\n\n// Starts in 78 AD,\nconst INDIAN_ERA_START = 78;\n\n// The Indian year starts 80 days later than the Gregorian year.\nconst INDIAN_YEAR_START = 80;\n\n/**\n * The Indian National Calendar is similar to the Gregorian calendar, but with\n * years numbered since the Saka era in 78 AD (Gregorian). There are 12 months\n * in each year, with either 30 or 31 days. Only one era identifier is supported: 'saka'.\n */\nexport class IndianCalendar extends GregorianCalendar {\n  identifier = 'indian';\n\n  fromJulianDay(jd: number): CalendarDate {\n    // Gregorian date for Julian day\n    let date = super.fromJulianDay(jd);\n\n    // Year in Saka era\n    let indianYear = date.year - INDIAN_ERA_START;\n\n    // Day number in Gregorian year (starting from 0)\n    let yDay = jd - gregorianToJulianDay(date.era, date.year, 1, 1);\n\n    let leapMonth: number;\n    if (yDay < INDIAN_YEAR_START) {\n      //  Day is at the end of the preceding Saka year\n      indianYear--;\n\n      // Days in leapMonth this year, previous Gregorian year\n      leapMonth = isLeapYear(date.year - 1) ? 31 : 30;\n      yDay += leapMonth + (31 * 5) + (30 * 3) + 10;\n    } else {\n      // Days in leapMonth this year\n      leapMonth = isLeapYear(date.year) ? 31 : 30;\n      yDay -= INDIAN_YEAR_START;\n    }\n\n    let indianMonth: number;\n    let indianDay: number;\n    if (yDay < leapMonth) {\n      indianMonth = 1;\n      indianDay = yDay + 1;\n    } else {\n      let mDay = yDay - leapMonth;\n      if (mDay < (31 * 5)) {\n        indianMonth = Math.floor(mDay / 31) + 2;\n        indianDay = (mDay % 31) + 1;\n      } else {\n        mDay -= 31 * 5;\n        indianMonth = Math.floor(mDay / 30) + 7;\n        indianDay = (mDay % 30) + 1;\n      }\n    }\n\n    return new CalendarDate(this, indianYear, indianMonth, indianDay);\n  }\n\n  toJulianDay(date: AnyCalendarDate) {\n    let extendedYear = date.year + INDIAN_ERA_START;\n    let [era, year] = fromExtendedYear(extendedYear);\n\n    let leapMonth: number;\n    let jd: number;\n    if (isLeapYear(year)) {\n      leapMonth = 31;\n      jd = gregorianToJulianDay(era, year, 3, 21);\n    } else {\n      leapMonth = 30;\n      jd = gregorianToJulianDay(era, year, 3, 22);\n    }\n\n    if (date.month === 1) {\n      return jd + date.day - 1;\n    }\n\n    jd += leapMonth + Math.min(date.month - 2, 5) * 31;\n\n    if (date.month >= 8) {\n      jd += (date.month - 7) * 30;\n    }\n\n    jd += date.day - 1;\n    return jd;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    if (date.month === 1 && isLeapYear(date.year + INDIAN_ERA_START)) {\n      return 31;\n    }\n\n    if (date.month >= 2 && date.month <= 6) {\n      return 31;\n    }\n\n    return 30;\n  }\n\n  getYearsInEra(): number {\n    // 9999-12-31 gregorian is 9920-10-10 indian.\n    // Round down to 9919 for the last full year.\n    return 9919;\n  }\n\n  getEras() {\n    return ['saka'];\n  }\n\n  balanceDate() {}\n}\n"], "names": [], "version": 3, "file": "IndianCalendar.main.js.map"}