var $625ad1e1f4c43bc1$exports = require("./CalendarDate.main.js");


function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "mod", () => $a5090d6430502238$export$842a2cf37af977e1);
/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 
function $a5090d6430502238$export$842a2cf37af977e1(amount, numerator) {
    return amount - numerator * Math.floor(amount / numerator);
}
function $a5090d6430502238$export$784d13d8ee351f07(date) {
    if (date.era) return new (0, $625ad1e1f4c43bc1$exports.CalendarDate)(date.calendar, date.era, date.year, date.month, date.day);
    else return new (0, $625ad1e1f4c43bc1$exports.CalendarDate)(date.calendar, date.year, date.month, date.day);
}
function $a5090d6430502238$export$27fa0172ae2644b3(date) {
    if (date.era) return new (0, $625ad1e1f4c43bc1$exports.CalendarDateTime)(date.calendar, date.era, date.year, date.month, date.day, date.hour, date.minute, date.second, date.millisecond);
    else return new (0, $625ad1e1f4c43bc1$exports.CalendarDateTime)(date.calendar, date.year, date.month, date.day, date.hour, date.minute, date.second);
}


//# sourceMappingURL=utils.main.js.map
