{"mappings": ";;;AAAA;;;;;;;;;;CAUC;;AAQD,MAAM,iCAAW;AAKV,SAAS,0CAAI,IAAqC,EAAE,QAA0B;IACnF,IAAI,cAAsD,KAAK,IAAI;IACnE,IAAI,OAAO,UAAU,cAAc,oCAAc,aAAa,YAAY;IAE1E,+BAAS,aAAa,SAAS,KAAK,IAAI;IACxC,IAAI,YAAY,QAAQ,CAAC,gBAAgB,EACvC,YAAY,QAAQ,CAAC,gBAAgB,CAAC,aAAa;IAGrD,YAAY,KAAK,IAAI,SAAS,MAAM,IAAI;IAExC,uCAAiB;IACjB,wCAAkB;IAElB,YAAY,GAAG,IAAI,AAAC,CAAA,SAAS,KAAK,IAAI,CAAA,IAAK;IAC3C,YAAY,GAAG,IAAI,SAAS,IAAI,IAAI;IACpC,YAAY,GAAG,IAAI;IAEnB,iCAAW;IAEX,IAAI,YAAY,QAAQ,CAAC,WAAW,EAClC,YAAY,QAAQ,CAAC,WAAW,CAAC;IAGnC,iGAAiG;IACjG,+FAA+F;IAC/F,+FAA+F;IAC/F,uGAAuG;IACvG,IAAI,YAAY,IAAI,GAAG,GAAG;QACxB,YAAY,IAAI,GAAG;QACnB,YAAY,KAAK,GAAG;QACpB,YAAY,GAAG,GAAG;IACpB;IAEA,IAAI,UAAU,YAAY,QAAQ,CAAC,aAAa,CAAC;IACjD,IAAI,YAAY,IAAI,GAAG,SAAS;YACX,oCAAA;QAAnB,IAAI,gBAAe,qCAAA,CAAA,wBAAA,YAAY,QAAQ,EAAC,YAAY,cAAjC,yDAAA,wCAAA,uBAAoC;QACvD,YAAY,IAAI,GAAG;QACnB,YAAY,KAAK,GAAG,eAAe,IAAI,YAAY,QAAQ,CAAC,eAAe,CAAC;QAC5E,YAAY,GAAG,GAAG,eAAe,IAAI,YAAY,QAAQ,CAAC,cAAc,CAAC;IAC3E;IAEA,IAAI,YAAY,KAAK,GAAG,GAAG;QACzB,YAAY,KAAK,GAAG;QACpB,YAAY,GAAG,GAAG;IACpB;IAEA,IAAI,WAAW,YAAY,QAAQ,CAAC,eAAe,CAAC;IACpD,IAAI,YAAY,KAAK,GAAG,UAAU;QAChC,YAAY,KAAK,GAAG;QACpB,YAAY,GAAG,GAAG,YAAY,QAAQ,CAAC,cAAc,CAAC;IACxD;IAEA,YAAY,GAAG,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,YAAY,QAAQ,CAAC,cAAc,CAAC,cAAc,YAAY,GAAG;IACxG,OAAO;AACT;AAEA,SAAS,+BAAS,IAA8B,EAAE,KAAa;QACzD,6BAAA;IAAJ,KAAI,8BAAA,CAAA,iBAAA,KAAK,QAAQ,EAAC,YAAY,cAA1B,kDAAA,iCAAA,gBAA6B,OAC/B,QAAQ,CAAC;IAGX,KAAK,IAAI,IAAI;AACf;AAEA,SAAS,uCAAiB,IAA8B;IACtD,MAAO,KAAK,KAAK,GAAG,EAAG;QACrB,+BAAS,MAAM;QACf,KAAK,KAAK,IAAI,KAAK,QAAQ,CAAC,eAAe,CAAC;IAC9C;IAEA,IAAI,eAAe;IACnB,MAAO,KAAK,KAAK,GAAI,CAAA,eAAe,KAAK,QAAQ,CAAC,eAAe,CAAC,KAAI,EAAI;QACxE,KAAK,KAAK,IAAI;QACd,+BAAS,MAAM;IACjB;AACF;AAEA,SAAS,iCAAW,IAA8B;IAChD,MAAO,KAAK,GAAG,GAAG,EAAG;QACnB,KAAK,KAAK;QACV,uCAAiB;QACjB,KAAK,GAAG,IAAI,KAAK,QAAQ,CAAC,cAAc,CAAC;IAC3C;IAEA,MAAO,KAAK,GAAG,GAAG,KAAK,QAAQ,CAAC,cAAc,CAAC,MAAO;QACpD,KAAK,GAAG,IAAI,KAAK,QAAQ,CAAC,cAAc,CAAC;QACzC,KAAK,KAAK;QACV,uCAAiB;IACnB;AACF;AAEA,SAAS,wCAAkB,IAA8B;IACvD,KAAK,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,QAAQ,CAAC,eAAe,CAAC,OAAO,KAAK,KAAK;IACjF,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,QAAQ,CAAC,cAAc,CAAC,OAAO,KAAK,GAAG;AAC9E;AAEO,SAAS,0CAAU,IAA8B;IACtD,IAAI,KAAK,QAAQ,CAAC,aAAa,EAC7B,KAAK,QAAQ,CAAC,aAAa,CAAC;IAG9B,KAAK,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,QAAQ,CAAC,aAAa,CAAC,OAAO,KAAK,IAAI;IAC7E,wCAAkB;AACpB;AAEO,SAAS,0CAAe,QAA0B;IACvD,IAAI,kBAAkB,CAAC;IACvB,IAAK,IAAI,OAAO,SACd,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,UAC3B,eAAe,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI;IAIzC,OAAO;AACT;AAIO,SAAS,0CAAS,IAAqC,EAAE,QAA0B;IACxF,OAAO,0CAAI,MAAM,0CAAe;AAClC;AAIO,SAAS,0CAAI,IAAqC,EAAE,MAAkB;IAC3E,IAAI,cAAwC,KAAK,IAAI;IAErD,IAAI,OAAO,GAAG,IAAI,MAChB,YAAY,GAAG,GAAG,OAAO,GAAG;IAG9B,IAAI,OAAO,IAAI,IAAI,MACjB,YAAY,IAAI,GAAG,OAAO,IAAI;IAGhC,IAAI,OAAO,KAAK,IAAI,MAClB,YAAY,KAAK,GAAG,OAAO,KAAK;IAGlC,IAAI,OAAO,GAAG,IAAI,MAChB,YAAY,GAAG,GAAG,OAAO,GAAG;IAG9B,0CAAU;IACV,OAAO;AACT;AAIO,SAAS,0CAAQ,KAA8B,EAAE,MAAkB;IACxE,IAAI,eAAiD,MAAM,IAAI;IAE/D,IAAI,OAAO,IAAI,IAAI,MACjB,aAAa,IAAI,GAAG,OAAO,IAAI;IAGjC,IAAI,OAAO,MAAM,IAAI,MACnB,aAAa,MAAM,GAAG,OAAO,MAAM;IAGrC,IAAI,OAAO,MAAM,IAAI,MACnB,aAAa,MAAM,GAAG,OAAO,MAAM;IAGrC,IAAI,OAAO,WAAW,IAAI,MACxB,aAAa,WAAW,GAAG,OAAO,WAAW;IAG/C,0CAAc;IACd,OAAO;AACT;AAEA,SAAS,kCAAY,IAAsB;IACzC,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,WAAW,GAAG;IAC7C,KAAK,WAAW,GAAG,qCAAe,KAAK,WAAW,EAAE;IAEpD,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,GAAG;IACxC,KAAK,MAAM,GAAG,qCAAe,KAAK,MAAM,EAAE;IAE1C,KAAK,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,GAAG;IACtC,KAAK,MAAM,GAAG,qCAAe,KAAK,MAAM,EAAE;IAE1C,IAAI,OAAO,KAAK,KAAK,CAAC,KAAK,IAAI,GAAG;IAClC,KAAK,IAAI,GAAG,qCAAe,KAAK,IAAI,EAAE;IAEtC,OAAO;AACT;AAEO,SAAS,0CAAc,IAAsB;IAClD,KAAK,WAAW,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,WAAW,EAAE;IAC1D,KAAK,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE;IAChD,KAAK,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE;IAChD,KAAK,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,IAAI,EAAE;AAC9C;AAEA,SAAS,qCAAe,CAAS,EAAE,CAAS;IAC1C,IAAI,SAAS,IAAI;IACjB,IAAI,SAAS,GACX,UAAU;IAEZ,OAAO;AACT;AAEA,SAAS,oCAAc,IAAsB,EAAE,QAAsB;IACnE,KAAK,IAAI,IAAI,SAAS,KAAK,IAAI;IAC/B,KAAK,MAAM,IAAI,SAAS,OAAO,IAAI;IACnC,KAAK,MAAM,IAAI,SAAS,OAAO,IAAI;IACnC,KAAK,WAAW,IAAI,SAAS,YAAY,IAAI;IAC7C,OAAO,kCAAY;AACrB;AAEO,SAAS,0CAAQ,IAAU,EAAE,QAAsB;IACxD,IAAI,MAAM,KAAK,IAAI;IACnB,oCAAc,KAAK;IACnB,OAAO;AACT;AAEO,SAAS,0CAAa,IAAU,EAAE,QAAsB;IAC7D,OAAO,0CAAQ,MAAM,0CAAe;AACtC;AAIO,SAAS,0CAAU,KAAsC,EAAE,KAAgB,EAAE,MAAc,EAAE,OAAsB;IACxH,IAAI,UAAoD,MAAM,IAAI;IAElE,OAAQ;QACN,KAAK;YAAO;gBACV,IAAI,OAAO,MAAM,QAAQ,CAAC,OAAO;gBACjC,IAAI,WAAW,KAAK,OAAO,CAAC,MAAM,GAAG;gBACrC,IAAI,WAAW,GACb,MAAM,IAAI,MAAM,kBAAkB,MAAM,GAAG;gBAE7C,WAAW,iCAAW,UAAU,QAAQ,GAAG,KAAK,MAAM,GAAG,GAAG,oBAAA,8BAAA,QAAS,KAAK;gBAC1E,QAAQ,GAAG,GAAG,IAAI,CAAC,SAAS;gBAE5B,uGAAuG;gBACvG,0CAAU;gBACV;YACF;QACA,KAAK;gBACC,gCAAA;YAAJ,KAAI,iCAAA,CAAA,oBAAA,QAAQ,QAAQ,EAAC,YAAY,cAA7B,qDAAA,oCAAA,mBAAgC,UAClC,SAAS,CAAC;YAGZ,0GAA0G;YAC1G,2GAA2G;YAC3G,0FAA0F;YAC1F,QAAQ,IAAI,GAAG,iCAAW,MAAM,IAAI,EAAE,QAAQ,CAAC,UAAU,MAAM,oBAAA,8BAAA,QAAS,KAAK;YAC7E,IAAI,QAAQ,IAAI,KAAK,CAAC,UACpB,QAAQ,IAAI,GAAG;YAGjB,IAAI,QAAQ,QAAQ,CAAC,gBAAgB,EACnC,QAAQ,QAAQ,CAAC,gBAAgB,CAAC,SAAS;YAE7C;QAEF,KAAK;YACH,QAAQ,KAAK,GAAG,iCAAW,MAAM,KAAK,EAAE,QAAQ,GAAG,MAAM,QAAQ,CAAC,eAAe,CAAC,QAAQ,oBAAA,8BAAA,QAAS,KAAK;YACxG;QACF,KAAK;YACH,QAAQ,GAAG,GAAG,iCAAW,MAAM,GAAG,EAAE,QAAQ,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,QAAQ,oBAAA,8BAAA,QAAS,KAAK;YACnG;QACF;YACE,MAAM,IAAI,MAAM,uBAAuB;IAC3C;IAEA,IAAI,MAAM,QAAQ,CAAC,WAAW,EAC5B,MAAM,QAAQ,CAAC,WAAW,CAAC;IAG7B,0CAAU;IACV,OAAO;AACT;AAIO,SAAS,0CAAU,KAA8B,EAAE,KAAgB,EAAE,MAAc,EAAE,OAA0B;IACpH,IAAI,UAA4C,MAAM,IAAI;IAE1D,OAAQ;QACN,KAAK;YAAQ;gBACX,IAAI,QAAQ,MAAM,IAAI;gBACtB,IAAI,MAAM;gBACV,IAAI,MAAM;gBACV,IAAI,CAAA,oBAAA,8BAAA,QAAS,SAAS,MAAK,IAAI;oBAC7B,IAAI,OAAO,SAAS;oBACpB,MAAM,OAAO,KAAK;oBAClB,MAAM,OAAO,KAAK;gBACpB;gBACA,QAAQ,IAAI,GAAG,iCAAW,OAAO,QAAQ,KAAK,KAAK,oBAAA,8BAAA,QAAS,KAAK;gBACjE;YACF;QACA,KAAK;YACH,QAAQ,MAAM,GAAG,iCAAW,MAAM,MAAM,EAAE,QAAQ,GAAG,IAAI,oBAAA,8BAAA,QAAS,KAAK;YACvE;QACF,KAAK;YACH,QAAQ,MAAM,GAAG,iCAAW,MAAM,MAAM,EAAE,QAAQ,GAAG,IAAI,oBAAA,8BAAA,QAAS,KAAK;YACvE;QACF,KAAK;YACH,QAAQ,WAAW,GAAG,iCAAW,MAAM,WAAW,EAAE,QAAQ,GAAG,KAAK,oBAAA,8BAAA,QAAS,KAAK;YAClF;QACF;YACE,MAAM,IAAI,MAAM,uBAAuB;IAC3C;IAEA,OAAO;AACT;AAEA,SAAS,iCAAW,KAAa,EAAE,MAAc,EAAE,GAAW,EAAE,GAAW,EAAE,QAAQ,KAAK;IACxF,IAAI,OAAO;QACT,SAAS,KAAK,IAAI,CAAC;QAEnB,IAAI,QAAQ,KACV,QAAQ;QAGV,IAAI,MAAM,KAAK,GAAG,CAAC;QACnB,IAAI,SAAS,GACX,QAAQ,KAAK,IAAI,CAAC,QAAQ,OAAO;aAEjC,QAAQ,KAAK,KAAK,CAAC,QAAQ,OAAO;QAGpC,IAAI,QAAQ,KACV,QAAQ;IAEZ,OAAO;QACL,SAAS;QACT,IAAI,QAAQ,KACV,QAAQ,MAAO,CAAA,MAAM,QAAQ,CAAA;aACxB,IAAI,QAAQ,KACjB,QAAQ,MAAO,CAAA,QAAQ,MAAM,CAAA;IAEjC;IAEA,OAAO;AACT;AAEO,SAAS,0CAAS,QAAuB,EAAE,QAA0B;IAC1E,IAAI;IACJ,IAAI,AAAC,SAAS,KAAK,IAAI,QAAQ,SAAS,KAAK,KAAK,KAAO,SAAS,MAAM,IAAI,QAAQ,SAAS,MAAM,KAAK,KAAO,SAAS,KAAK,IAAI,QAAQ,SAAS,KAAK,KAAK,KAAO,SAAS,IAAI,IAAI,QAAQ,SAAS,IAAI,KAAK,GAAI;QAChN,IAAI,MAAM,0CAAI,CAAA,GAAA,yCAAiB,EAAE,WAAW;YAC1C,OAAO,SAAS,KAAK;YACrB,QAAQ,SAAS,MAAM;YACvB,OAAO,SAAS,KAAK;YACrB,MAAM,SAAS,IAAI;QACrB;QAEA,4EAA4E;QAC5E,yCAAyC;QACzC,KAAK,CAAA,GAAA,yCAAS,EAAE,KAAK,SAAS,QAAQ;IACxC,OACE,uDAAuD;IACvD,KAAK,CAAA,GAAA,wCAAY,EAAE,YAAY,SAAS,MAAM;IAGhD,wGAAwG;IACxG,wGAAwG;IACxG,oGAAoG;IACpG,MAAM,SAAS,YAAY,IAAI;IAC/B,MAAM,AAAC,CAAA,SAAS,OAAO,IAAI,CAAA,IAAK;IAChC,MAAM,AAAC,CAAA,SAAS,OAAO,IAAI,CAAA,IAArB;IACN,MAAM,AAAC,CAAA,SAAS,KAAK,IAAI,CAAA,IAAnB;IAEN,IAAI,MAAM,CAAA,GAAA,yCAAW,EAAE,IAAI,SAAS,QAAQ;IAC5C,OAAO,CAAA,GAAA,yCAAS,EAAE,KAAK,SAAS,QAAQ;AAC1C;AAEO,SAAS,0CAAc,QAAuB,EAAE,QAA0B;IAC/E,OAAO,0CAAS,UAAU,0CAAe;AAC3C;AAEO,SAAS,0CAAW,QAAuB,EAAE,KAA4B,EAAE,MAAc,EAAE,OAA0B;IAC1H,8HAA8H;IAC9H,mIAAmI;IACnI,yHAAyH;IACzH,OAAQ;QACN,KAAK;YAAQ;gBACX,IAAI,MAAM;gBACV,IAAI,MAAM;gBACV,IAAI,CAAA,oBAAA,8BAAA,QAAS,SAAS,MAAK,IAAI;oBAC7B,IAAI,OAAO,SAAS,IAAI,IAAI;oBAC5B,MAAM,OAAO,KAAK;oBAClB,MAAM,OAAO,KAAK;gBACpB;gBAEA,wEAAwE;gBACxE,gEAAgE;gBAChE,+EAA+E;gBAC/E,8EAA8E;gBAC9E,kCAAkC;gBAClC,IAAI,gBAAgB,CAAA,GAAA,yCAAiB,EAAE;gBACvC,IAAI,UAAU,CAAA,GAAA,yCAAS,EAAE,0CAAQ,eAAe;oBAAC,MAAM;gBAAG,IAAI,IAAI,CAAA,GAAA,yCAAgB;gBAClF,IAAI,cAAc;oBAAC,CAAA,GAAA,yCAAS,EAAE,SAAS,SAAS,QAAQ,EAAE;oBAAY,CAAA,GAAA,yCAAS,EAAE,SAAS,SAAS,QAAQ,EAAE;iBAAS,CACnH,MAAM,CAAC,CAAA,KAAM,CAAA,GAAA,yCAAW,EAAE,IAAI,SAAS,QAAQ,EAAE,GAAG,KAAK,QAAQ,GAAG,CAAC,CAAC,EAAE;gBAE3E,IAAI,UAAU,CAAA,GAAA,yCAAS,EAAE,0CAAQ,eAAe;oBAAC,MAAM;gBAAG,IAAI,IAAI,CAAA,GAAA,yCAAgB;gBAClF,IAAI,cAAc;oBAAC,CAAA,GAAA,yCAAS,EAAE,SAAS,SAAS,QAAQ,EAAE;oBAAY,CAAA,GAAA,yCAAS,EAAE,SAAS,SAAS,QAAQ,EAAE;iBAAS,CACnH,MAAM,CAAC,CAAA,KAAM,CAAA,GAAA,yCAAW,EAAE,IAAI,SAAS,QAAQ,EAAE,GAAG,KAAK,QAAQ,GAAG,EAAE,GAAG;gBAE5E,mFAAmF;gBACnF,gFAAgF;gBAChF,2CAA2C;gBAC3C,IAAI,KAAK,CAAA,GAAA,wCAAY,EAAE,YAAY,SAAS,MAAM;gBAClD,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAK;gBAC5B,IAAI,YAAY,KAAK;gBACrB,KAAK,iCACH,OACA,QACA,KAAK,KAAK,CAAC,cAAc,iCACzB,KAAK,KAAK,CAAC,cAAc,iCACzB,oBAAA,8BAAA,QAAS,KAAK,IACZ,iCAAW;gBAEf,yFAAyF;gBACzF,OAAO,CAAA,GAAA,yCAAS,EAAE,CAAA,GAAA,yCAAW,EAAE,IAAI,SAAS,QAAQ,GAAG,SAAS,QAAQ;YAC1E;QACA,KAAK;QACL,KAAK;QACL,KAAK;YACH,aAAa;YACb,OAAO,0CAAU,UAAU,OAAO,QAAQ;QAC5C,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAO;gBACV,IAAI,MAAM,0CAAU,CAAA,GAAA,yCAAiB,EAAE,WAAW,OAAO,QAAQ;gBACjE,IAAI,KAAK,CAAA,GAAA,yCAAS,EAAE,KAAK,SAAS,QAAQ;gBAC1C,OAAO,CAAA,GAAA,yCAAS,EAAE,CAAA,GAAA,yCAAW,EAAE,IAAI,SAAS,QAAQ,GAAG,SAAS,QAAQ;YAC1E;QACA;YACE,MAAM,IAAI,MAAM,uBAAuB;IAC3C;AACF;AAEO,SAAS,0CAAS,QAAuB,EAAE,MAA+B,EAAE,cAA+B;IAChH,qFAAqF;IACrF,wHAAwH;IACxH,IAAI,gBAAgB,CAAA,GAAA,yCAAiB,EAAE;IACvC,IAAI,MAAM,0CAAQ,0CAAI,eAAe,SAAS;IAE9C,+EAA+E;IAC/E,8EAA8E;IAC9E,IAAI,IAAI,OAAO,CAAC,mBAAmB,GACjC,OAAO;IAGT,IAAI,KAAK,CAAA,GAAA,yCAAS,EAAE,KAAK,SAAS,QAAQ,EAAE;IAC5C,OAAO,CAAA,GAAA,yCAAS,EAAE,CAAA,GAAA,yCAAW,EAAE,IAAI,SAAS,QAAQ,GAAG,SAAS,QAAQ;AAC1E", "sources": ["packages/@internationalized/date/src/manipulation.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AnyCalendarDate, AnyDateTime, AnyTime, CycleOptions, CycleTimeOptions, DateDuration, DateField, DateFields, DateTimeDuration, Disambiguation, TimeDuration, TimeField, TimeFields} from './types';\nimport {CalendarDate, CalendarDateTime, Time, ZonedDateTime} from './CalendarDate';\nimport {epochFromDate, fromAbsolute, toAbsolute, toCalendar, toCalendarDateTime} from './conversion';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {Mutable} from './utils';\n\nconst ONE_HOUR = 3600000;\n\nexport function add(date: CalendarDateTime, duration: DateTimeDuration): CalendarDateTime;\nexport function add(date: CalendarDate, duration: DateDuration): CalendarDate;\nexport function add(date: CalendarDate | CalendarDateTime, duration: DateTimeDuration): CalendarDate | CalendarDateTime;\nexport function add(date: CalendarDate | CalendarDateTime, duration: DateTimeDuration) {\n  let mutableDate: Mutable<AnyCalendarDate | AnyDateTime> = date.copy();\n  let days = 'hour' in mutableDate ? addTimeFields(mutableDate, duration) : 0;\n\n  addYears(mutableDate, duration.years || 0);\n  if (mutableDate.calendar.balanceYearMonth) {\n    mutableDate.calendar.balanceYearMonth(mutableDate, date);\n  }\n\n  mutableDate.month += duration.months || 0;\n\n  balanceYearMonth(mutableDate);\n  constrainMonthDay(mutableDate);\n\n  mutableDate.day += (duration.weeks || 0) * 7;\n  mutableDate.day += duration.days || 0;\n  mutableDate.day += days;\n\n  balanceDay(mutableDate);\n\n  if (mutableDate.calendar.balanceDate) {\n    mutableDate.calendar.balanceDate(mutableDate);\n  }\n\n  // Constrain in case adding ended up with a date outside the valid range for the calendar system.\n  // The behavior here is slightly different than when constraining in the `set` function in that\n  // we adjust smaller fields to their minimum/maximum values rather than constraining each field\n  // individually. This matches the general behavior of `add` vs `set` regarding how fields are balanced.\n  if (mutableDate.year < 1) {\n    mutableDate.year = 1;\n    mutableDate.month = 1;\n    mutableDate.day = 1;\n  }\n\n  let maxYear = mutableDate.calendar.getYearsInEra(mutableDate);\n  if (mutableDate.year > maxYear) {\n    let isInverseEra = mutableDate.calendar.isInverseEra?.(mutableDate);\n    mutableDate.year = maxYear;\n    mutableDate.month = isInverseEra ? 1 : mutableDate.calendar.getMonthsInYear(mutableDate);\n    mutableDate.day = isInverseEra ? 1 : mutableDate.calendar.getDaysInMonth(mutableDate);\n  }\n\n  if (mutableDate.month < 1) {\n    mutableDate.month = 1;\n    mutableDate.day = 1;\n  }\n\n  let maxMonth = mutableDate.calendar.getMonthsInYear(mutableDate);\n  if (mutableDate.month > maxMonth) {\n    mutableDate.month = maxMonth;\n    mutableDate.day = mutableDate.calendar.getDaysInMonth(mutableDate);\n  }\n\n  mutableDate.day = Math.max(1, Math.min(mutableDate.calendar.getDaysInMonth(mutableDate), mutableDate.day));\n  return mutableDate;\n}\n\nfunction addYears(date: Mutable<AnyCalendarDate>, years: number) {\n  if (date.calendar.isInverseEra?.(date)) {\n    years = -years;\n  }\n\n  date.year += years;\n}\n\nfunction balanceYearMonth(date: Mutable<AnyCalendarDate>) {\n  while (date.month < 1) {\n    addYears(date, -1);\n    date.month += date.calendar.getMonthsInYear(date);\n  }\n\n  let monthsInYear = 0;\n  while (date.month > (monthsInYear = date.calendar.getMonthsInYear(date))) {\n    date.month -= monthsInYear;\n    addYears(date, 1);\n  }\n}\n\nfunction balanceDay(date: Mutable<AnyCalendarDate>) {\n  while (date.day < 1) {\n    date.month--;\n    balanceYearMonth(date);\n    date.day += date.calendar.getDaysInMonth(date);\n  }\n\n  while (date.day > date.calendar.getDaysInMonth(date)) {\n    date.day -= date.calendar.getDaysInMonth(date);\n    date.month++;\n    balanceYearMonth(date);\n  }\n}\n\nfunction constrainMonthDay(date: Mutable<AnyCalendarDate>) {\n  date.month = Math.max(1, Math.min(date.calendar.getMonthsInYear(date), date.month));\n  date.day = Math.max(1, Math.min(date.calendar.getDaysInMonth(date), date.day));\n}\n\nexport function constrain(date: Mutable<AnyCalendarDate>) {\n  if (date.calendar.constrainDate) {\n    date.calendar.constrainDate(date);\n  }\n\n  date.year = Math.max(1, Math.min(date.calendar.getYearsInEra(date), date.year));\n  constrainMonthDay(date);\n}\n\nexport function invertDuration(duration: DateTimeDuration): DateTimeDuration {\n  let inverseDuration = {};\n  for (let key in duration) {\n    if (typeof duration[key] === 'number') {\n      inverseDuration[key] = -duration[key];\n    }\n  }\n\n  return inverseDuration;\n}\n\nexport function subtract(date: CalendarDateTime, duration: DateTimeDuration): CalendarDateTime;\nexport function subtract(date: CalendarDate, duration: DateDuration): CalendarDate;\nexport function subtract(date: CalendarDate | CalendarDateTime, duration: DateTimeDuration): CalendarDate | CalendarDateTime {\n  return add(date, invertDuration(duration));\n}\n\nexport function set(date: CalendarDateTime, fields: DateFields): CalendarDateTime;\nexport function set(date: CalendarDate, fields: DateFields): CalendarDate;\nexport function set(date: CalendarDate | CalendarDateTime, fields: DateFields) {\n  let mutableDate: Mutable<AnyCalendarDate> = date.copy();\n\n  if (fields.era != null) {\n    mutableDate.era = fields.era;\n  }\n\n  if (fields.year != null) {\n    mutableDate.year = fields.year;\n  }\n\n  if (fields.month != null) {\n    mutableDate.month = fields.month;\n  }\n\n  if (fields.day != null) {\n    mutableDate.day = fields.day;\n  }\n\n  constrain(mutableDate);\n  return mutableDate;\n}\n\nexport function setTime(value: CalendarDateTime, fields: TimeFields): CalendarDateTime;\nexport function setTime(value: Time, fields: TimeFields): Time;\nexport function setTime(value: Time | CalendarDateTime, fields: TimeFields) {\n  let mutableValue: Mutable<Time | CalendarDateTime> = value.copy();\n\n  if (fields.hour != null) {\n    mutableValue.hour = fields.hour;\n  }\n\n  if (fields.minute != null) {\n    mutableValue.minute = fields.minute;\n  }\n\n  if (fields.second != null) {\n    mutableValue.second = fields.second;\n  }\n\n  if (fields.millisecond != null) {\n    mutableValue.millisecond = fields.millisecond;\n  }\n\n  constrainTime(mutableValue);\n  return mutableValue;\n}\n\nfunction balanceTime(time: Mutable<AnyTime>): number {\n  time.second += Math.floor(time.millisecond / 1000);\n  time.millisecond = nonNegativeMod(time.millisecond, 1000);\n\n  time.minute += Math.floor(time.second / 60);\n  time.second = nonNegativeMod(time.second, 60);\n\n  time.hour += Math.floor(time.minute / 60);\n  time.minute = nonNegativeMod(time.minute, 60);\n\n  let days = Math.floor(time.hour / 24);\n  time.hour = nonNegativeMod(time.hour, 24);\n\n  return days;\n}\n\nexport function constrainTime(time: Mutable<AnyTime>) {\n  time.millisecond = Math.max(0, Math.min(time.millisecond, 1000));\n  time.second = Math.max(0, Math.min(time.second, 59));\n  time.minute = Math.max(0, Math.min(time.minute, 59));\n  time.hour = Math.max(0, Math.min(time.hour, 23));\n}\n\nfunction nonNegativeMod(a: number, b: number) {\n  let result = a % b;\n  if (result < 0) {\n    result += b;\n  }\n  return result;\n}\n\nfunction addTimeFields(time: Mutable<AnyTime>, duration: TimeDuration): number {\n  time.hour += duration.hours || 0;\n  time.minute += duration.minutes || 0;\n  time.second += duration.seconds || 0;\n  time.millisecond += duration.milliseconds || 0;\n  return balanceTime(time);\n}\n\nexport function addTime(time: Time, duration: TimeDuration): Time {\n  let res = time.copy();\n  addTimeFields(res, duration);\n  return res;\n}\n\nexport function subtractTime(time: Time, duration: TimeDuration): Time {\n  return addTime(time, invertDuration(duration));\n}\n\nexport function cycleDate(value: CalendarDateTime, field: DateField, amount: number, options?: CycleOptions): CalendarDateTime;\nexport function cycleDate(value: CalendarDate, field: DateField, amount: number, options?: CycleOptions): CalendarDate;\nexport function cycleDate(value: CalendarDate | CalendarDateTime, field: DateField, amount: number, options?: CycleOptions) {\n  let mutable: Mutable<CalendarDate | CalendarDateTime> = value.copy();\n\n  switch (field) {\n    case 'era': {\n      let eras = value.calendar.getEras();\n      let eraIndex = eras.indexOf(value.era);\n      if (eraIndex < 0) {\n        throw new Error('Invalid era: ' + value.era);\n      }\n      eraIndex = cycleValue(eraIndex, amount, 0, eras.length - 1, options?.round);\n      mutable.era = eras[eraIndex];\n\n      // Constrain the year and other fields within the era, so the era doesn't change when we balance below.\n      constrain(mutable);\n      break;\n    }\n    case 'year': {\n      if (mutable.calendar.isInverseEra?.(mutable)) {\n        amount = -amount;\n      }\n\n      // The year field should not cycle within the era as that can cause weird behavior affecting other fields.\n      // We need to also allow values < 1 so that decrementing goes to the previous era. If we get -Infinity back\n      // we know we wrapped around after reaching 9999 (the maximum), so set the year back to 1.\n      mutable.year = cycleValue(value.year, amount, -Infinity, 9999, options?.round);\n      if (mutable.year === -Infinity) {\n        mutable.year = 1;\n      }\n\n      if (mutable.calendar.balanceYearMonth) {\n        mutable.calendar.balanceYearMonth(mutable, value);\n      }\n      break;\n    }\n    case 'month':\n      mutable.month = cycleValue(value.month, amount, 1, value.calendar.getMonthsInYear(value), options?.round);\n      break;\n    case 'day':\n      mutable.day = cycleValue(value.day, amount, 1, value.calendar.getDaysInMonth(value), options?.round);\n      break;\n    default:\n      throw new Error('Unsupported field ' + field);\n  }\n\n  if (value.calendar.balanceDate) {\n    value.calendar.balanceDate(mutable);\n  }\n\n  constrain(mutable);\n  return mutable;\n}\n\nexport function cycleTime(value: CalendarDateTime, field: TimeField, amount: number, options?: CycleTimeOptions): CalendarDateTime;\nexport function cycleTime(value: Time, field: TimeField, amount: number, options?: CycleTimeOptions): Time;\nexport function cycleTime(value: Time | CalendarDateTime, field: TimeField, amount: number, options?: CycleTimeOptions) {\n  let mutable: Mutable<Time | CalendarDateTime> = value.copy();\n\n  switch (field) {\n    case 'hour': {\n      let hours = value.hour;\n      let min = 0;\n      let max = 23;\n      if (options?.hourCycle === 12) {\n        let isPM = hours >= 12;\n        min = isPM ? 12 : 0;\n        max = isPM ? 23 : 11;\n      }\n      mutable.hour = cycleValue(hours, amount, min, max, options?.round);\n      break;\n    }\n    case 'minute':\n      mutable.minute = cycleValue(value.minute, amount, 0, 59, options?.round);\n      break;\n    case 'second':\n      mutable.second = cycleValue(value.second, amount, 0, 59, options?.round);\n      break;\n    case 'millisecond':\n      mutable.millisecond = cycleValue(value.millisecond, amount, 0, 999, options?.round);\n      break;\n    default:\n      throw new Error('Unsupported field ' + field);\n  }\n\n  return mutable;\n}\n\nfunction cycleValue(value: number, amount: number, min: number, max: number, round = false) {\n  if (round) {\n    value += Math.sign(amount);\n\n    if (value < min) {\n      value = max;\n    }\n\n    let div = Math.abs(amount);\n    if (amount > 0) {\n      value = Math.ceil(value / div) * div;\n    } else {\n      value = Math.floor(value / div) * div;\n    }\n\n    if (value > max) {\n      value = min;\n    }\n  } else {\n    value += amount;\n    if (value < min) {\n      value = max - (min - value - 1);\n    } else if (value > max) {\n      value = min + (value - max - 1);\n    }\n  }\n\n  return value;\n}\n\nexport function addZoned(dateTime: ZonedDateTime, duration: DateTimeDuration): ZonedDateTime {\n  let ms: number;\n  if ((duration.years != null && duration.years !== 0) || (duration.months != null && duration.months !== 0) || (duration.weeks != null && duration.weeks !== 0) || (duration.days != null && duration.days !== 0)) {\n    let res = add(toCalendarDateTime(dateTime), {\n      years: duration.years,\n      months: duration.months,\n      weeks: duration.weeks,\n      days: duration.days\n    });\n\n    // Changing the date may change the timezone offset, so we need to recompute\n    // using the 'compatible' disambiguation.\n    ms = toAbsolute(res, dateTime.timeZone);\n  } else {\n    // Otherwise, preserve the offset of the original date.\n    ms = epochFromDate(dateTime) - dateTime.offset;\n  }\n\n  // Perform time manipulation in milliseconds rather than on the original time fields to account for DST.\n  // For example, adding one hour during a DST transition may result in the hour field staying the same or\n  // skipping an hour. This results in the offset field changing value instead of the specified field.\n  ms += duration.milliseconds || 0;\n  ms += (duration.seconds || 0) * 1000;\n  ms += (duration.minutes || 0) * 60 * 1000;\n  ms += (duration.hours || 0) * 60 * 60 * 1000;\n\n  let res = fromAbsolute(ms, dateTime.timeZone);\n  return toCalendar(res, dateTime.calendar);\n}\n\nexport function subtractZoned(dateTime: ZonedDateTime, duration: DateTimeDuration): ZonedDateTime {\n  return addZoned(dateTime, invertDuration(duration));\n}\n\nexport function cycleZoned(dateTime: ZonedDateTime, field: DateField | TimeField, amount: number, options?: CycleTimeOptions): ZonedDateTime {\n  // For date fields, we want the time to remain consistent and the UTC offset to potentially change to account for DST changes.\n  // For time fields, we want the time to change by the amount given. This may result in the hour field staying the same, but the UTC\n  // offset changing in the case of a backward DST transition, or skipping an hour in the case of a forward DST transition.\n  switch (field) {\n    case 'hour': {\n      let min = 0;\n      let max = 23;\n      if (options?.hourCycle === 12) {\n        let isPM = dateTime.hour >= 12;\n        min = isPM ? 12 : 0;\n        max = isPM ? 23 : 11;\n      }\n\n      // The minimum and maximum hour may be affected by daylight saving time.\n      // For example, it might jump forward at midnight, and skip 1am.\n      // Or it might end at midnight and repeat the 11pm hour. To handle this, we get\n      // the possible absolute times for the min and max, and find the maximum range\n      // that is within the current day.\n      let plainDateTime = toCalendarDateTime(dateTime);\n      let minDate = toCalendar(setTime(plainDateTime, {hour: min}), new GregorianCalendar());\n      let minAbsolute = [toAbsolute(minDate, dateTime.timeZone, 'earlier'), toAbsolute(minDate, dateTime.timeZone, 'later')]\n        .filter(ms => fromAbsolute(ms, dateTime.timeZone).day === minDate.day)[0];\n\n      let maxDate = toCalendar(setTime(plainDateTime, {hour: max}), new GregorianCalendar());\n      let maxAbsolute = [toAbsolute(maxDate, dateTime.timeZone, 'earlier'), toAbsolute(maxDate, dateTime.timeZone, 'later')]\n        .filter(ms => fromAbsolute(ms, dateTime.timeZone).day === maxDate.day).pop()!;\n\n      // Since hours may repeat, we need to operate on the absolute time in milliseconds.\n      // This is done in hours from the Unix epoch so that cycleValue works correctly,\n      // and then converted back to milliseconds.\n      let ms = epochFromDate(dateTime) - dateTime.offset;\n      let hours = Math.floor(ms / ONE_HOUR);\n      let remainder = ms % ONE_HOUR;\n      ms = cycleValue(\n        hours,\n        amount,\n        Math.floor(minAbsolute / ONE_HOUR),\n        Math.floor(maxAbsolute / ONE_HOUR),\n        options?.round\n      ) * ONE_HOUR + remainder;\n\n      // Now compute the new timezone offset, and convert the absolute time back to local time.\n      return toCalendar(fromAbsolute(ms, dateTime.timeZone), dateTime.calendar);\n    }\n    case 'minute':\n    case 'second':\n    case 'millisecond':\n      // @ts-ignore\n      return cycleTime(dateTime, field, amount, options);\n    case 'era':\n    case 'year':\n    case 'month':\n    case 'day': {\n      let res = cycleDate(toCalendarDateTime(dateTime), field, amount, options);\n      let ms = toAbsolute(res, dateTime.timeZone);\n      return toCalendar(fromAbsolute(ms, dateTime.timeZone), dateTime.calendar);\n    }\n    default:\n      throw new Error('Unsupported field ' + field);\n  }\n}\n\nexport function setZoned(dateTime: ZonedDateTime, fields: DateFields & TimeFields, disambiguation?: Disambiguation): ZonedDateTime {\n  // Set the date/time fields, and recompute the UTC offset to account for DST changes.\n  // We also need to validate by converting back to a local time in case hours are skipped during forward DST transitions.\n  let plainDateTime = toCalendarDateTime(dateTime);\n  let res = setTime(set(plainDateTime, fields), fields);\n\n  // If the resulting plain date time values are equal, return the original time.\n  // We don't want to change the offset when setting the time to the same value.\n  if (res.compare(plainDateTime) === 0) {\n    return dateTime;\n  }\n\n  let ms = toAbsolute(res, dateTime.timeZone, disambiguation);\n  return toCalendar(fromAbsolute(ms, dateTime.timeZone), dateTime.calendar);\n}\n"], "names": [], "version": 3, "file": "manipulation.module.js.map"}