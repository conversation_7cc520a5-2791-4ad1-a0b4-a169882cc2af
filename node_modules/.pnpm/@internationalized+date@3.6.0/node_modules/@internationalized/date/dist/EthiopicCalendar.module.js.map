{"mappings": ";;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;;AAMhG,MAAM,uCAAiB;AACvB,MAAM,qCAAe;AAErB,oDAAoD;AACpD,iBAAiB;AACjB,MAAM,2CAAqB;AAE3B,SAAS,oCAAc,KAAa,EAAE,IAAY,EAAE,KAAa,EAAE,GAAW;IAC5E,OACE,MAAwB,wCAAwC;OAC9D,MAAM,KAAgB,4BAA4B;OAClD,KAAK,KAAK,CAAC,OAAO,GAAI,yBAAyB;OAC/C,KAAM,CAAA,QAAQ,EAAQ,uCAAuC;IAA/C,IACd,MAAM,EAAgB,6CAA6C;;AAEzE;AAEA,SAAS,oCAAc,KAAa,EAAE,EAAU;IAC9C,IAAI,OAAO,KAAK,KAAK,CAAC,AAAC,IAAK,CAAA,KAAK,KAAI,IAAM;IAC3C,IAAI,QAAQ,IAAI,KAAK,KAAK,CAAC,AAAC,CAAA,KAAK,oCAAc,OAAO,MAAM,GAAG,EAAC,IAAK;IACrE,IAAI,MAAM,KAAK,IAAI,oCAAc,OAAO,MAAM,OAAO;IACrD,OAAO;QAAC;QAAM;QAAO;KAAI;AAC3B;AAEA,SAAS,iCAAW,IAAY;IAC9B,OAAO,KAAK,KAAK,CAAC,AAAC,OAAO,IAAK;AACjC;AAEA,SAAS,qCAAe,IAAY,EAAE,KAAa;IACjD,4EAA4E;IAC5E,gFAAgF;IAChF,8EAA8E;IAC9E,iFAAiF;IACjF,sBAAsB;IACtB,IAAI,QAAQ,OAAO,GACjB,wBAAwB;IACxB,OAAO;SAEP,+CAA+C;IAC/C,OAAO,iCAAW,QAAQ;AAE9B;AAOO,MAAM;IAGX,cAAc,EAAU,EAAgB;QACtC,IAAI,CAAC,MAAM,OAAO,IAAI,GAAG,oCAAc,sCAAgB;QACvD,IAAI,MAAM;QACV,IAAI,QAAQ,GAAG;YACb,MAAM;YACN,QAAQ;QACV;QAEA,OAAO,IAAI,CAAA,GAAA,yCAAW,EAAE,IAAI,EAAE,KAAK,MAAM,OAAO;IAClD;IAEA,YAAY,IAAqB,EAAE;QACjC,IAAI,OAAO,KAAK,IAAI;QACpB,IAAI,KAAK,GAAG,KAAK,MACf,QAAQ;QAGV,OAAO,oCAAc,sCAAgB,MAAM,KAAK,KAAK,EAAE,KAAK,GAAG;IACjE;IAEA,eAAe,IAAqB,EAAU;QAC5C,OAAO,qCAAe,KAAK,IAAI,EAAE,KAAK,KAAK;IAC7C;IAEA,kBAA0B;QACxB,OAAO;IACT;IAEA,cAAc,IAAqB,EAAU;QAC3C,OAAO,MAAM,iCAAW,KAAK,IAAI;IACnC;IAEA,cAAc,IAAqB,EAAU;QAC3C,+CAA+C;QAC/C,6CAA6C;QAC7C,kDAAkD;QAClD,OAAO,KAAK,GAAG,KAAK,OAAO,OAAO;IACpC;IAEA,UAAU;QACR,OAAO;YAAC;YAAM;SAAK;IACrB;;aA3CA,aAAa;;AA4Cf;AAMO,MAAM,kDAAkC;IAG7C,cAAc,EAAU,EAAgB;QACtC,IAAI,CAAC,MAAM,OAAO,IAAI,GAAG,oCAAc,sCAAgB;QACvD,QAAQ;QACR,OAAO,IAAI,CAAA,GAAA,yCAAW,EAAE,IAAI,EAAE,MAAM,MAAM,OAAO;IACnD;IAEA,UAAU;QACR,OAAO;YAAC;SAAK;IACf;IAEA,gBAAwB;QACtB,uFAAuF;QACvF,OAAO;IACT;;6BAfA,aAAa,UAAW,6CAA6C;;;AAgBvE;AAOO,MAAM,kDAAuB;IAGlC,cAAc,EAAU,EAAgB;QACtC,IAAI,CAAC,MAAM,OAAO,IAAI,GAAG,oCAAc,oCAAc;QACrD,IAAI,MAAM;QACV,IAAI,QAAQ,GAAG;YACb,MAAM;YACN,OAAO,IAAI;QACb;QAEA,OAAO,IAAI,CAAA,GAAA,yCAAW,EAAE,IAAI,EAAE,KAAK,MAAM,OAAO;IAClD;IAEA,YAAY,IAAqB,EAAE;QACjC,IAAI,OAAO,KAAK,IAAI;QACpB,IAAI,KAAK,GAAG,KAAK,OACf,OAAO,IAAI;QAGb,OAAO,oCAAc,oCAAc,MAAM,KAAK,KAAK,EAAE,KAAK,GAAG;IAC/D;IAEA,eAAe,IAAqB,EAAU;QAC5C,IAAI,OAAO,KAAK,IAAI;QACpB,IAAI,KAAK,GAAG,KAAK,OACf,OAAO,IAAI;QAGb,OAAO,qCAAe,MAAM,KAAK,KAAK;IACxC;IAEA,aAAa,IAAqB,EAAW;QAC3C,OAAO,KAAK,GAAG,KAAK;IACtB;IAEA,YAAY,IAA8B,EAAE;QAC1C,IAAI,KAAK,IAAI,IAAI,GAAG;YAClB,KAAK,GAAG,GAAG,KAAK,GAAG,KAAK,QAAQ,OAAO;YACvC,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI;QAC3B;IACF;IAEA,UAAU;QACR,OAAO;YAAC;YAAO;SAAK;IACtB;IAEA,cAAc,IAAqB,EAAU;QAC3C,6CAA6C;QAC7C,6CAA6C;QAC7C,oDAAoD;QACpD,OAAO,KAAK,GAAG,KAAK,QAAQ,OAAO;IACrC;;6BAnDA,aAAa;;AAoDf", "sources": ["packages/@internationalized/date/src/calendars/EthiopicCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {Mutable} from '../utils';\n\nconst ETHIOPIC_EPOCH = 1723856;\nconst COPTIC_EPOCH = 1824665;\n\n// The delta between Amete Alem 1 and Amete Mihret 1\n// AA 5501 = AM 1\nconst AMETE_MIHRET_DELTA = 5500;\n\nfunction ceToJulianDay(epoch: number, year: number, month: number, day: number): number {\n  return (\n    epoch                   // difference from Julian epoch to 1,1,1\n    + 365 * year            // number of days from years\n    + Math.floor(year / 4)  // extra day of leap year\n    + 30 * (month - 1)      // number of days from months (1 based)\n    + day - 1               // number of days for present month (1 based)\n  );\n}\n\nfunction julianDayToCE(epoch: number, jd: number) {\n  let year = Math.floor((4 * (jd - epoch)) / 1461);\n  let month = 1 + Math.floor((jd - ceToJulianDay(epoch, year, 1, 1)) / 30);\n  let day = jd + 1 - ceToJulianDay(epoch, year, month, 1);\n  return [year, month, day];\n}\n\nfunction getLeapDay(year: number) {\n  return Math.floor((year % 4) / 3);\n}\n\nfunction getDaysInMonth(year: number, month: number) {\n  // The Ethiopian and Coptic calendars have 13 months, 12 of 30 days each and\n  // an intercalary month at the end of the year of 5 or 6 days, depending whether\n  // the year is a leap year or not. The Leap Year follows the same rules as the\n  // Julian Calendar so that the extra month always has six days in the year before\n  // a Julian Leap Year.\n  if (month % 13 !== 0) {\n    // not intercalary month\n    return 30;\n  } else {\n    // intercalary month 5 days + possible leap day\n    return getLeapDay(year) + 5;\n  }\n}\n\n/**\n * The Ethiopic calendar system is the official calendar used in Ethiopia.\n * It includes 12 months of 30 days each, plus 5 or 6 intercalary days depending\n * on whether it is a leap year. Two eras are supported: 'AA' and 'AM'.\n */\nexport class EthiopicCalendar implements Calendar {\n  identifier = 'ethiopic';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let [year, month, day] = julianDayToCE(ETHIOPIC_EPOCH, jd);\n    let era = 'AM';\n    if (year <= 0) {\n      era = 'AA';\n      year += AMETE_MIHRET_DELTA;\n    }\n\n    return new CalendarDate(this, era, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate) {\n    let year = date.year;\n    if (date.era === 'AA') {\n      year -= AMETE_MIHRET_DELTA;\n    }\n\n    return ceToJulianDay(ETHIOPIC_EPOCH, year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return getDaysInMonth(date.year, date.month);\n  }\n\n  getMonthsInYear(): number {\n    return 13;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return 365 + getLeapDay(date.year);\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    // 9999-12-31 gregorian is 9992-20-02 ethiopic.\n    // Round down to 9991 for the last full year.\n    // AA 9999-01-01 ethiopic is 4506-09-30 gregorian.\n    return date.era === 'AA' ? 9999 : 9991;\n  }\n\n  getEras() {\n    return ['AA', 'AM'];\n  }\n}\n\n/**\n * The Ethiopic (Amete Alem) calendar is the same as the modern Ethiopic calendar,\n * except years were measured from a different epoch. Only one era is supported: 'AA'.\n */\nexport class EthiopicAmeteAlemCalendar extends EthiopicCalendar {\n  identifier = 'ethioaa'; // also known as 'ethiopic-amete-alem' in ICU\n\n  fromJulianDay(jd: number): CalendarDate {\n    let [year, month, day] = julianDayToCE(ETHIOPIC_EPOCH, jd);\n    year += AMETE_MIHRET_DELTA;\n    return new CalendarDate(this, 'AA', year, month, day);\n  }\n\n  getEras() {\n    return ['AA'];\n  }\n\n  getYearsInEra(): number {\n    // 9999-13-04 ethioaa is the maximum date, which is equivalent to 4506-09-29 gregorian.\n    return 9999;\n  }\n}\n\n/**\n * The Coptic calendar is similar to the Ethiopic calendar.\n * It includes 12 months of 30 days each, plus 5 or 6 intercalary days depending\n * on whether it is a leap year. Two eras are supported: 'BCE' and 'CE'.\n */\nexport class CopticCalendar extends EthiopicCalendar {\n  identifier = 'coptic';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let [year, month, day] = julianDayToCE(COPTIC_EPOCH, jd);\n    let era = 'CE';\n    if (year <= 0) {\n      era = 'BCE';\n      year = 1 - year;\n    }\n\n    return new CalendarDate(this, era, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate) {\n    let year = date.year;\n    if (date.era === 'BCE') {\n      year = 1 - year;\n    }\n\n    return ceToJulianDay(COPTIC_EPOCH, year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    let year = date.year;\n    if (date.era === 'BCE') {\n      year = 1 - year;\n    }\n\n    return getDaysInMonth(year, date.month);\n  }\n\n  isInverseEra(date: AnyCalendarDate): boolean {\n    return date.era === 'BCE';\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>) {\n    if (date.year <= 0) {\n      date.era = date.era === 'BCE' ? 'CE' : 'BCE';\n      date.year = 1 - date.year;\n    }\n  }\n\n  getEras() {\n    return ['BCE', 'CE'];\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    // 9999-12-30 gregorian is 9716-02-20 coptic.\n    // Round down to 9715 for the last full year.\n    // BCE 9999-01-01 coptic is BC 9716-06-15 gregorian.\n    return date.era === 'BCE' ? 9999 : 9715;\n  }\n}\n"], "names": [], "version": 3, "file": "EthiopicCalendar.module.js.map"}