{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;;;AAMhG,MAAM,2CAAqB;AAOpB,MAAM,kDAAyB,CAAA,GAAA,2CAAgB;IAGpD,cAAc,EAAU,EAAgB;QACtC,IAAI,gBAAgB,KAAK,CAAC,cAAc;QACxC,IAAI,OAAO,CAAA,GAAA,yCAAc,EAAE,cAAc,GAAG,EAAE,cAAc,IAAI;QAChE,OAAO,IAAI,CAAA,GAAA,sCAAW,EACpB,IAAI,EACJ,OAAO,0CACP,cAAc,KAAK,EACnB,cAAc,GAAG;IAErB;IAEA,YAAY,IAAqB,EAAE;QACjC,OAAO,KAAK,CAAC,YAAY,kCAAY;IACvC;IAEA,UAAU;QACR,OAAO;YAAC;SAAK;IACf;IAEA,eAAe,IAAqB,EAAU;QAC5C,OAAO,KAAK,CAAC,eAAe,kCAAY;IAC1C;IAEA,cAAc,CAAC;;6BAzBf,aAAa;;AA0Bf;AAEA,SAAS,kCAAY,IAAqB;IACxC,IAAI,CAAC,KAAK,KAAK,GAAG,CAAA,GAAA,0CAAe,EAAE,KAAK,IAAI,GAAG;IAC/C,OAAO,IAAI,CAAA,GAAA,sCAAW,EACpB,KACA,MACA,KAAK,KAAK,EACV,KAAK,GAAG;AAEZ", "sources": ["packages/@internationalized/date/src/calendars/BuddhistCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {fromExtendedYear, getExtendedYear, GregorianCalendar} from './GregorianCalendar';\n\nconst BUDDHIST_ERA_START = -543;\n\n/**\n * The Buddhist calendar is the same as the Gregorian calendar, but counts years\n * starting from the birth of Buddha in 543 BC (<PERSON><PERSON>). It supports only one\n * era, identified as 'BE'.\n */\nexport class BuddhistCalendar extends GregorianCalendar {\n  identifier = 'buddhist';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let gregorianDate = super.fromJulianDay(jd);\n    let year = getExtendedYear(gregorianDate.era, gregorianDate.year);\n    return new CalendarDate(\n      this,\n      year - BUDDHIST_ERA_START,\n      gregorianDate.month,\n      gregorianDate.day\n    );\n  }\n\n  toJulianDay(date: AnyCalendarDate) {\n    return super.toJulianDay(toGregorian(date));\n  }\n\n  getEras() {\n    return ['BE'];\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return super.getDaysInMonth(toGregorian(date));\n  }\n\n  balanceDate() {}\n}\n\nfunction toGregorian(date: AnyCalendarDate) {\n  let [era, year] = fromExtendedYear(date.year + BUDDHIST_ERA_START);\n  return new CalendarDate(\n    era,\n    year,\n    date.month,\n    date.day\n  );\n}\n"], "names": [], "version": 3, "file": "BuddhistCalendar.main.js.map"}