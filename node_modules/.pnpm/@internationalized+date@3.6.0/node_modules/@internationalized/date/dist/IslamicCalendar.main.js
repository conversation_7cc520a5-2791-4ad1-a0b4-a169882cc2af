var $625ad1e1f4c43bc1$exports = require("./CalendarDate.main.js");


function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "IslamicCivilCalendar", () => $ecb2c4cc8c9aae25$export$2066795aadd37bfc);
$parcel$export(module.exports, "IslamicTabularCalendar", () => $ecb2c4cc8c9aae25$export$37f0887f2f9d22f7);
$parcel$export(module.exports, "IslamicUmalquraCalendar", () => $ecb2c4cc8c9aae25$export$5baab4758c231076);
/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ // Portions of the code in this file are based on code from ICU.
// Original licensing can be found in the NOTICE file in the root directory of this source tree.

const $ecb2c4cc8c9aae25$var$CIVIL_EPOC = 1948440; // CE 622 July 16 Friday (Julian calendar) / CE 622 July 19 (Gregorian calendar)
const $ecb2c4cc8c9aae25$var$ASTRONOMICAL_EPOC = 1948439; // CE 622 July 15 Thursday (Julian calendar)
const $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START = 1300;
const $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_END = 1600;
const $ecb2c4cc8c9aae25$var$UMALQURA_START_DAYS = 460322;
function $ecb2c4cc8c9aae25$var$islamicToJulianDay(epoch, year, month, day) {
    return day + Math.ceil(29.5 * (month - 1)) + (year - 1) * 354 + Math.floor((3 + 11 * year) / 30) + epoch - 1;
}
function $ecb2c4cc8c9aae25$var$julianDayToIslamic(calendar, epoch, jd) {
    let year = Math.floor((30 * (jd - epoch) + 10646) / 10631);
    let month = Math.min(12, Math.ceil((jd - (29 + $ecb2c4cc8c9aae25$var$islamicToJulianDay(epoch, year, 1, 1))) / 29.5) + 1);
    let day = jd - $ecb2c4cc8c9aae25$var$islamicToJulianDay(epoch, year, month, 1) + 1;
    return new (0, $625ad1e1f4c43bc1$exports.CalendarDate)(calendar, year, month, day);
}
function $ecb2c4cc8c9aae25$var$isLeapYear(year) {
    return (14 + 11 * year) % 30 < 11;
}
class $ecb2c4cc8c9aae25$export$2066795aadd37bfc {
    fromJulianDay(jd) {
        return $ecb2c4cc8c9aae25$var$julianDayToIslamic(this, $ecb2c4cc8c9aae25$var$CIVIL_EPOC, jd);
    }
    toJulianDay(date) {
        return $ecb2c4cc8c9aae25$var$islamicToJulianDay($ecb2c4cc8c9aae25$var$CIVIL_EPOC, date.year, date.month, date.day);
    }
    getDaysInMonth(date) {
        let length = 29 + date.month % 2;
        if (date.month === 12 && $ecb2c4cc8c9aae25$var$isLeapYear(date.year)) length++;
        return length;
    }
    getMonthsInYear() {
        return 12;
    }
    getDaysInYear(date) {
        return $ecb2c4cc8c9aae25$var$isLeapYear(date.year) ? 355 : 354;
    }
    getYearsInEra() {
        // 9999 gregorian
        return 9665;
    }
    getEras() {
        return [
            'AH'
        ];
    }
    constructor(){
        this.identifier = 'islamic-civil';
    }
}
class $ecb2c4cc8c9aae25$export$37f0887f2f9d22f7 extends $ecb2c4cc8c9aae25$export$2066795aadd37bfc {
    fromJulianDay(jd) {
        return $ecb2c4cc8c9aae25$var$julianDayToIslamic(this, $ecb2c4cc8c9aae25$var$ASTRONOMICAL_EPOC, jd);
    }
    toJulianDay(date) {
        return $ecb2c4cc8c9aae25$var$islamicToJulianDay($ecb2c4cc8c9aae25$var$ASTRONOMICAL_EPOC, date.year, date.month, date.day);
    }
    constructor(...args){
        super(...args), this.identifier = 'islamic-tbla';
    }
}
// Generated by scripts/generate-umalqura.js
const $ecb2c4cc8c9aae25$var$UMALQURA_DATA = 'qgpUDckO1AbqBmwDrQpVBakGkgepC9QF2gpcBS0NlQZKB1QLagutBa4ETwoXBYsGpQbVCtYCWwmdBE0KJg2VDawFtgm6AlsKKwWVCsoG6Qr0AnYJtgJWCcoKpAvSC9kF3AJtCU0FpQpSC6ULtAW2CVcFlwJLBaMGUgdlC2oFqworBZUMSg2lDcoF1gpXCasESwmlClILagt1BXYCtwhbBFUFqQW0BdoJ3QRuAjYJqgpUDbIN1QXaAlsJqwRVCkkLZAtxC7QFtQpVCiUNkg7JDtQG6QprCasEkwpJDaQNsg25CroEWworBZUKKgtVC1wFvQQ9Ah0JlQpKC1oLbQW2AjsJmwRVBqkGVAdqC2wFrQpVBSkLkgupC9QF2gpaBasKlQVJB2QHqgu1BbYCVgpNDiULUgtqC60FrgIvCZcESwalBqwG1gpdBZ0ETQoWDZUNqgW1BdoCWwmtBJUFygbkBuoK9QS2AlYJqgpUC9IL2QXqAm0JrQSVCkoLpQuyBbUJ1gSXCkcFkwZJB1ULagVrCisFiwpGDaMNygXWCtsEawJLCaUKUgtpC3UFdgG3CFsCKwVlBbQF2gntBG0BtgimClINqQ3UBdoKWwmrBFMGKQdiB6kLsgW1ClUFJQuSDckO0gbpCmsFqwRVCikNVA2qDbUJugQ7CpsETQqqCtUK2gJdCV4ELgqaDFUNsga5BroEXQotBZUKUguoC7QLuQXaAloJSgukDdEO6AZqC20FNQWVBkoNqA3UDdoGWwWdAisGFQtKC5ULqgWuCi4JjwwnBZUGqgbWCl0FnQI=';
let $ecb2c4cc8c9aae25$var$UMALQURA_MONTHLENGTH;
let $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START_TABLE;
function $ecb2c4cc8c9aae25$var$umalquraYearStart(year) {
    return $ecb2c4cc8c9aae25$var$UMALQURA_START_DAYS + $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START_TABLE[year - $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START];
}
function $ecb2c4cc8c9aae25$var$umalquraMonthLength(year, month) {
    let idx = year - $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START;
    let mask = 0x01 << 11 - (month - 1);
    if (($ecb2c4cc8c9aae25$var$UMALQURA_MONTHLENGTH[idx] & mask) === 0) return 29;
    else return 30;
}
function $ecb2c4cc8c9aae25$var$umalquraMonthStart(year, month) {
    let day = $ecb2c4cc8c9aae25$var$umalquraYearStart(year);
    for(let i = 1; i < month; i++)day += $ecb2c4cc8c9aae25$var$umalquraMonthLength(year, i);
    return day;
}
function $ecb2c4cc8c9aae25$var$umalquraYearLength(year) {
    return $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START_TABLE[year + 1 - $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START] - $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START_TABLE[year - $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START];
}
class $ecb2c4cc8c9aae25$export$5baab4758c231076 extends $ecb2c4cc8c9aae25$export$2066795aadd37bfc {
    fromJulianDay(jd) {
        let days = jd - $ecb2c4cc8c9aae25$var$CIVIL_EPOC;
        let startDays = $ecb2c4cc8c9aae25$var$umalquraYearStart($ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START);
        let endDays = $ecb2c4cc8c9aae25$var$umalquraYearStart($ecb2c4cc8c9aae25$var$UMALQURA_YEAR_END);
        if (days < startDays || days > endDays) return super.fromJulianDay(jd);
        else {
            let y = $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START - 1;
            let m = 1;
            let d = 1;
            while(d > 0){
                y++;
                d = days - $ecb2c4cc8c9aae25$var$umalquraYearStart(y) + 1;
                let yearLength = $ecb2c4cc8c9aae25$var$umalquraYearLength(y);
                if (d === yearLength) {
                    m = 12;
                    break;
                } else if (d < yearLength) {
                    let monthLength = $ecb2c4cc8c9aae25$var$umalquraMonthLength(y, m);
                    m = 1;
                    while(d > monthLength){
                        d -= monthLength;
                        m++;
                        monthLength = $ecb2c4cc8c9aae25$var$umalquraMonthLength(y, m);
                    }
                    break;
                }
            }
            return new (0, $625ad1e1f4c43bc1$exports.CalendarDate)(this, y, m, days - $ecb2c4cc8c9aae25$var$umalquraMonthStart(y, m) + 1);
        }
    }
    toJulianDay(date) {
        if (date.year < $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START || date.year > $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_END) return super.toJulianDay(date);
        return $ecb2c4cc8c9aae25$var$CIVIL_EPOC + $ecb2c4cc8c9aae25$var$umalquraMonthStart(date.year, date.month) + (date.day - 1);
    }
    getDaysInMonth(date) {
        if (date.year < $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START || date.year > $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_END) return super.getDaysInMonth(date);
        return $ecb2c4cc8c9aae25$var$umalquraMonthLength(date.year, date.month);
    }
    getDaysInYear(date) {
        if (date.year < $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START || date.year > $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_END) return super.getDaysInYear(date);
        return $ecb2c4cc8c9aae25$var$umalquraYearLength(date.year);
    }
    constructor(){
        super(), this.identifier = 'islamic-umalqura';
        if (!$ecb2c4cc8c9aae25$var$UMALQURA_MONTHLENGTH) $ecb2c4cc8c9aae25$var$UMALQURA_MONTHLENGTH = new Uint16Array(Uint8Array.from(atob($ecb2c4cc8c9aae25$var$UMALQURA_DATA), (c)=>c.charCodeAt(0)).buffer);
        if (!$ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START_TABLE) {
            $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START_TABLE = new Uint32Array($ecb2c4cc8c9aae25$var$UMALQURA_YEAR_END - $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START + 1);
            let yearStart = 0;
            for(let year = $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START; year <= $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_END; year++){
                $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START_TABLE[year - $ecb2c4cc8c9aae25$var$UMALQURA_YEAR_START] = yearStart;
                for(let i = 1; i <= 12; i++)yearStart += $ecb2c4cc8c9aae25$var$umalquraMonthLength(year, i);
            }
        }
    }
}


//# sourceMappingURL=IslamicCalendar.main.js.map
