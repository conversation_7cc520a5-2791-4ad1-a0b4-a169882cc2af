{"mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;AASD,SAAS,gCAAU,IAAW;IAC5B,IAAI,WAAqB,OAAO,IAAI,CAAC,EAAE,KAAK,WACxC,KAAK,KAAK,KACV,IAAI,CAAA,GAAA,yCAAgB;IAExB,IAAI;IACJ,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,UACrB,MAAM,KAAK,KAAK;SACX;QACL,IAAI,OAAO,SAAS,OAAO;QAC3B,MAAM,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;IAC7B;IAEA,IAAI,OAAO,KAAK,KAAK;IACrB,IAAI,QAAQ,KAAK,KAAK;IACtB,IAAI,MAAM,KAAK,KAAK;IAEpB,OAAO;QAAC;QAAU;QAAK;QAAM;QAAO;KAAI;AAC1C;IAIE,oFAAoF;AACpF,2FAA2F;AAC3F,0EAA0E;AAC1E,aAAa;AACb;AALK,MAAM;IAoCX,iCAAiC,GACjC,OAAqB;QACnB,IAAI,IAAI,CAAC,GAAG,EACV,OAAO,IAAI,0CAAa,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG;aAEhF,OAAO,IAAI,0CAAa,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG;IAE1E;IAEA,sEAAsE,GACtE,IAAI,QAAsB,EAAgB;QACxC,OAAO,CAAA,GAAA,yCAAE,EAAE,IAAI,EAAE;IACnB;IAEA,6EAA6E,GAC7E,SAAS,QAAsB,EAAgB;QAC7C,OAAO,CAAA,GAAA,yCAAO,EAAE,IAAI,EAAE;IACxB;IAEA,iIAAiI,GACjI,IAAI,MAAkB,EAAgB;QACpC,OAAO,CAAA,GAAA,yCAAE,EAAE,IAAI,EAAE;IACnB;IAEA;;;GAGC,GACD,MAAM,KAAgB,EAAE,MAAc,EAAE,OAAsB,EAAgB;QAC5E,OAAO,CAAA,GAAA,yCAAQ,EAAE,IAAI,EAAE,OAAO,QAAQ;IACxC;IAEA,gHAAgH,GAChH,OAAO,QAAgB,EAAQ;QAC7B,OAAO,CAAA,GAAA,yCAAK,EAAE,IAAI,EAAE;IACtB;IAEA,uDAAuD,GACvD,WAAmB;QACjB,OAAO,CAAA,GAAA,yCAAW,EAAE,IAAI;IAC1B;IAEA,yJAAyJ,GACzJ,QAAQ,CAAkB,EAAU;QAClC,OAAO,CAAA,GAAA,yCAAU,EAAE,IAAI,EAAE;IAC3B;IAxDA,YAAY,GAAG,IAAW,CAAE;QApB5B,oBAAA;;mBAAA,KAAA;;QAqBE,IAAI,CAAC,UAAU,KAAK,MAAM,OAAO,IAAI,GAAG,gCAAU;QAClD,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,GAAG,GAAG;QAEX,CAAA,GAAA,yCAAQ,EAAE,IAAI;IAChB;AAgDF;IAIE,oFAAoF;AACpF,aAAa;AACb;AAHK,MAAM;IA0BX,iCAAiC,GACjC,OAAa;QACX,OAAO,IAAI,yCAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW;IACvE;IAEA,8DAA8D,GAC9D,IAAI,QAAsB,EAAE;QAC1B,OAAO,CAAA,GAAA,yCAAM,EAAE,IAAI,EAAE;IACvB;IAEA,qEAAqE,GACrE,SAAS,QAAsB,EAAE;QAC/B,OAAO,CAAA,GAAA,yCAAW,EAAE,IAAI,EAAE;IAC5B;IAEA,yHAAyH,GACzH,IAAI,MAAkB,EAAE;QACtB,OAAO,CAAA,GAAA,yCAAM,EAAE,IAAI,EAAE;IACvB;IAEA;;;GAGC,GACD,MAAM,KAAgB,EAAE,MAAc,EAAE,OAA0B,EAAE;QAClE,OAAO,CAAA,GAAA,yCAAQ,EAAE,IAAI,EAAE,OAAO,QAAQ;IACxC;IAEA,uDAAuD,GACvD,WAAW;QACT,OAAO,CAAA,GAAA,yCAAW,EAAE,IAAI;IAC1B;IAEA,yJAAyJ,GACzJ,QAAQ,CAAU,EAAE;QAClB,OAAO,CAAA,GAAA,yCAAU,EAAE,IAAI,EAAE;IAC3B;IAjDA,YACE,OAAe,CAAC,EAChB,SAAiB,CAAC,EAClB,SAAiB,CAAC,EAClB,cAAsB,CAAC,CACvB;QAfF,oBAAA;;mBAAA,KAAA;;QAgBE,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,WAAW,GAAG;QACnB,CAAA,GAAA,yCAAY,EAAE,IAAI;IACpB;AAuCF;IAIE,oFAAoF;AACpF,aAAa;AACb;AAHK,MAAM;IA8CX,iCAAiC,GACjC,OAAyB;QACvB,IAAI,IAAI,CAAC,GAAG,EACV,OAAO,IAAI,0CAAiB,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW;aAE3I,OAAO,IAAI,0CAAiB,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW;IAErI;IAEA,0EAA0E,GAC1E,IAAI,QAA0B,EAAoB;QAChD,OAAO,CAAA,GAAA,yCAAE,EAAE,IAAI,EAAE;IACnB;IAEA,iFAAiF,GACjF,SAAS,QAA0B,EAAoB;QACrD,OAAO,CAAA,GAAA,yCAAO,EAAE,IAAI,EAAE;IACxB;IAEA,qIAAqI,GACrI,IAAI,MAA+B,EAAoB;QACrD,OAAO,CAAA,GAAA,yCAAE,EAAE,CAAA,GAAA,yCAAM,EAAE,IAAI,EAAE,SAAS;IACpC;IAEA;;;GAGC,GACD,MAAM,KAA4B,EAAE,MAAc,EAAE,OAA0B,EAAoB;QAChG,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,CAAA,GAAA,yCAAQ,EAAE,IAAI,EAAE,OAAO,QAAQ;YACxC;gBACE,OAAO,CAAA,GAAA,yCAAQ,EAAE,IAAI,EAAE,OAAO,QAAQ;QAC1C;IACF;IAEA,iFAAiF,GACjF,OAAO,QAAgB,EAAE,cAA+B,EAAQ;QAC9D,OAAO,CAAA,GAAA,yCAAK,EAAE,IAAI,EAAE,UAAU;IAChC;IAEA,uDAAuD,GACvD,WAAmB;QACjB,OAAO,CAAA,GAAA,yCAAe,EAAE,IAAI;IAC9B;IAEA,yJAAyJ,GACzJ,QAAQ,CAAkD,EAAU;QAClE,IAAI,MAAM,CAAA,GAAA,yCAAU,EAAE,IAAI,EAAE;QAC5B,IAAI,QAAQ,GACV,OAAO,CAAA,GAAA,yCAAU,EAAE,IAAI,EAAE,CAAA,GAAA,yCAAiB,EAAE;QAG9C,OAAO;IACT;IAzEA,YAAY,GAAG,IAAW,CAAE;QA5B5B,oBAAA;;mBAAA,KAAA;;QA6BE,IAAI,CAAC,UAAU,KAAK,MAAM,OAAO,IAAI,GAAG,gCAAU;QAClD,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,MAAM;QAC5B,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,MAAM;QAC9B,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,MAAM;QAC9B,IAAI,CAAC,WAAW,GAAG,KAAK,KAAK,MAAM;QAEnC,CAAA,GAAA,yCAAQ,EAAE,IAAI;IAChB;AA6DF;IAIE,oFAAoF;AACpF,aAAa;AACb;AAHK,MAAM;IAsDX,iCAAiC,GACjC,OAAsB;QACpB,IAAI,IAAI,CAAC,GAAG,EACV,OAAO,IAAI,0CAAc,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW;aAEpK,OAAO,IAAI,0CAAc,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW;IAE9J;IAEA,uEAAuE,GACvE,IAAI,QAA0B,EAAE;QAC9B,OAAO,CAAA,GAAA,yCAAO,EAAE,IAAI,EAAE;IACxB;IAEA,8EAA8E,GAC9E,SAAS,QAA0B,EAAE;QACnC,OAAO,CAAA,GAAA,yCAAY,EAAE,IAAI,EAAE;IAC7B;IAEA,kIAAkI,GAClI,IAAI,MAA+B,EAAE,cAA+B,EAAE;QACpE,OAAO,CAAA,GAAA,yCAAO,EAAE,IAAI,EAAE,QAAQ;IAChC;IAEA;;;GAGC,GACD,MAAM,KAA4B,EAAE,MAAc,EAAE,OAA0B,EAAE;QAC9E,OAAO,CAAA,GAAA,yCAAS,EAAE,IAAI,EAAE,OAAO,QAAQ;IACzC;IAEA,0DAA0D,GAC1D,SAAS;QACP,OAAO,CAAA,GAAA,wCAAU,EAAE,IAAI;IACzB;IAEC,0GAA0G,GAC3G,WAAW;QACT,OAAO,CAAA,GAAA,yCAAoB,EAAE,IAAI;IACnC;IAEC,8DAA8D,GAC/D,mBAAmB;QACjB,OAAO,IAAI,CAAC,MAAM,GAAG,WAAW;IAClC;IAEA,yJAAyJ,GACzJ,QAAQ,CAAkD,EAAE;QAC1D,6BAA6B;QAC7B,OAAO,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK,CAAA,GAAA,yCAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO;IAC7E;IAtEA,YAAY,GAAG,IAAW,CAAE;QAhC5B,oBAAA;;mBAAA,KAAA;;QAiCE,IAAI,CAAC,UAAU,KAAK,MAAM,OAAO,IAAI,GAAG,gCAAU;QAClD,IAAI,WAAW,KAAK,KAAK;QACzB,IAAI,SAAS,KAAK,KAAK;QACvB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,MAAM;QAC5B,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,MAAM;QAC9B,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,MAAM;QAC9B,IAAI,CAAC,WAAW,GAAG,KAAK,KAAK,MAAM;QAEnC,CAAA,GAAA,yCAAQ,EAAE,IAAI;IAChB;AAsDF", "sources": ["packages/@internationalized/date/src/CalendarDate.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {add, addTime, addZoned, constrain, constrainTime, cycleDate, cycleTime, cycleZoned, set, setTime, setZoned, subtract, subtractTime, subtractZoned} from './manipulation';\nimport {AnyCalendarDate, AnyTime, Calendar, CycleOptions, CycleTimeOptions, DateDuration, DateField, DateFields, DateTimeDuration, Disambiguation, TimeDuration, TimeField, TimeFields} from './types';\nimport {compareDate, compareTime} from './queries';\nimport {dateTimeToString, dateToString, timeToString, zonedDateTimeToString} from './string';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {toCalendarDateTime, toDate, toZoned, zonedToDate} from './conversion';\n\nfunction shiftArgs(args: any[]) {\n  let calendar: Calendar = typeof args[0] === 'object'\n    ? args.shift()\n    : new GregorianCalendar();\n\n  let era: string;\n  if (typeof args[0] === 'string') {\n    era = args.shift();\n  } else {\n    let eras = calendar.getEras();\n    era = eras[eras.length - 1];\n  }\n\n  let year = args.shift();\n  let month = args.shift();\n  let day = args.shift();\n\n  return [calendar, era, year, month, day];\n}\n\n/** A CalendarDate represents a date without any time components in a specific calendar system. */\nexport class CalendarDate {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // i.e. a ZonedDateTime should not be be passable to a parameter that expects CalendarDate.\n  // If that behavior is desired, use the AnyCalendarDate interface instead.\n  // @ts-ignore\n  #type;\n  /** The calendar system associated with this date, e.g. Gregorian. */\n  public readonly calendar: Calendar;\n  /** The calendar era for this date, e.g. \"BC\" or \"AD\". */\n  public readonly era: string;\n  /** The year of this date within the era. */\n  public readonly year: number;\n  /**\n   * The month number within the year. Note that some calendar systems such as Hebrew\n   * may have a variable number of months per year. Therefore, month numbers may not\n   * always correspond to the same month names in different years.\n   */\n  public readonly month: number;\n  /** The day number within the month. */\n  public readonly day: number;\n\n  constructor(year: number, month: number, day: number);\n  constructor(era: string, year: number, month: number, day: number);\n  constructor(calendar: Calendar, year: number, month: number, day: number);\n  constructor(calendar: Calendar, era: string, year: number, month: number, day: number);\n  constructor(...args: any[]) {\n    let [calendar, era, year, month, day] = shiftArgs(args);\n    this.calendar = calendar;\n    this.era = era;\n    this.year = year;\n    this.month = month;\n    this.day = day;\n\n    constrain(this);\n  }\n\n  /** Returns a copy of this date. */\n  copy(): CalendarDate {\n    if (this.era) {\n      return new CalendarDate(this.calendar, this.era, this.year, this.month, this.day);\n    } else {\n      return new CalendarDate(this.calendar, this.year, this.month, this.day);\n    }\n  }\n\n  /** Returns a new `CalendarDate` with the given duration added to it. */\n  add(duration: DateDuration): CalendarDate {\n    return add(this, duration);\n  }\n\n  /** Returns a new `CalendarDate` with the given duration subtracted from it. */\n  subtract(duration: DateDuration): CalendarDate {\n    return subtract(this, duration);\n  }\n\n  /** Returns a new `CalendarDate` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: DateFields): CalendarDate {\n    return set(this, fields);\n  }\n\n  /**\n   * Returns a new `CalendarDate` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: DateField, amount: number, options?: CycleOptions): CalendarDate {\n    return cycleDate(this, field, amount, options);\n  }\n\n  /** Converts the date to a native JavaScript Date object, with the time set to midnight in the given time zone. */\n  toDate(timeZone: string): Date {\n    return toDate(this, timeZone);\n  }\n\n  /** Converts the date to an ISO 8601 formatted string. */\n  toString(): string {\n    return dateToString(this);\n  }\n\n  /** Compares this date with another. A negative result indicates that this date is before the given one, and a positive date indicates that it is after. */\n  compare(b: AnyCalendarDate): number {\n    return compareDate(this, b);\n  }\n}\n\n/** A Time represents a clock time without any date components. */\nexport class Time {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // @ts-ignore\n  #type;\n  /** The hour, numbered from 0 to 23. */\n  public readonly hour: number;\n  /** The minute in the hour. */\n  public readonly minute: number;\n  /** The second in the minute. */\n  public readonly second: number;\n  /** The millisecond in the second. */\n  public readonly millisecond: number;\n\n  constructor(\n    hour: number = 0,\n    minute: number = 0,\n    second: number = 0,\n    millisecond: number = 0\n  ) {\n    this.hour = hour;\n    this.minute = minute;\n    this.second = second;\n    this.millisecond = millisecond;\n    constrainTime(this);\n  }\n\n  /** Returns a copy of this time. */\n  copy(): Time {\n    return new Time(this.hour, this.minute, this.second, this.millisecond);\n  }\n\n  /** Returns a new `Time` with the given duration added to it. */\n  add(duration: TimeDuration) {\n    return addTime(this, duration);\n  }\n\n  /** Returns a new `Time` with the given duration subtracted from it. */\n  subtract(duration: TimeDuration) {\n    return subtractTime(this, duration);\n  }\n\n  /** Returns a new `Time` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: TimeFields) {\n    return setTime(this, fields);\n  }\n\n  /**\n   * Returns a new `Time` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: TimeField, amount: number, options?: CycleTimeOptions) {\n    return cycleTime(this, field, amount, options);\n  }\n\n  /** Converts the time to an ISO 8601 formatted string. */\n  toString() {\n    return timeToString(this);\n  }\n\n  /** Compares this time with another. A negative result indicates that this time is before the given one, and a positive time indicates that it is after. */\n  compare(b: AnyTime) {\n    return compareTime(this, b);\n  }\n}\n\n/** A CalendarDateTime represents a date and time without a time zone, in a specific calendar system. */\nexport class CalendarDateTime {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // @ts-ignore\n  #type;\n  /** The calendar system associated with this date, e.g. Gregorian. */\n  public readonly calendar: Calendar;\n  /** The calendar era for this date, e.g. \"BC\" or \"AD\". */\n  public readonly era: string;\n  /** The year of this date within the era. */\n  public readonly year: number;\n  /**\n   * The month number within the year. Note that some calendar systems such as Hebrew\n   * may have a variable number of months per year. Therefore, month numbers may not\n   * always correspond to the same month names in different years.\n   */\n  public readonly month: number;\n  /** The day number within the month. */\n  public readonly day: number;\n  /** The hour in the day, numbered from 0 to 23. */\n  public readonly hour: number;\n  /** The minute in the hour. */\n  public readonly minute: number;\n  /** The second in the minute. */\n  public readonly second: number;\n  /** The millisecond in the second. */\n  public readonly millisecond: number;\n\n  constructor(year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(era: string, year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, era: string, year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(...args: any[]) {\n    let [calendar, era, year, month, day] = shiftArgs(args);\n    this.calendar = calendar;\n    this.era = era;\n    this.year = year;\n    this.month = month;\n    this.day = day;\n    this.hour = args.shift() || 0;\n    this.minute = args.shift() || 0;\n    this.second = args.shift() || 0;\n    this.millisecond = args.shift() || 0;\n\n    constrain(this);\n  }\n\n  /** Returns a copy of this date. */\n  copy(): CalendarDateTime {\n    if (this.era) {\n      return new CalendarDateTime(this.calendar, this.era, this.year, this.month, this.day, this.hour, this.minute, this.second, this.millisecond);\n    } else {\n      return new CalendarDateTime(this.calendar, this.year, this.month, this.day, this.hour, this.minute, this.second, this.millisecond);\n    }\n  }\n\n  /** Returns a new `CalendarDateTime` with the given duration added to it. */\n  add(duration: DateTimeDuration): CalendarDateTime {\n    return add(this, duration);\n  }\n\n  /** Returns a new `CalendarDateTime` with the given duration subtracted from it. */\n  subtract(duration: DateTimeDuration): CalendarDateTime {\n    return subtract(this, duration);\n  }\n\n  /** Returns a new `CalendarDateTime` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: DateFields & TimeFields): CalendarDateTime {\n    return set(setTime(this, fields), fields);\n  }\n\n  /**\n   * Returns a new `CalendarDateTime` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: DateField | TimeField, amount: number, options?: CycleTimeOptions): CalendarDateTime {\n    switch (field) {\n      case 'era':\n      case 'year':\n      case 'month':\n      case 'day':\n        return cycleDate(this, field, amount, options);\n      default:\n        return cycleTime(this, field, amount, options);\n    }\n  }\n\n  /** Converts the date to a native JavaScript Date object in the given time zone. */\n  toDate(timeZone: string, disambiguation?: Disambiguation): Date {\n    return toDate(this, timeZone, disambiguation);\n  }\n\n  /** Converts the date to an ISO 8601 formatted string. */\n  toString(): string {\n    return dateTimeToString(this);\n  }\n\n  /** Compares this date with another. A negative result indicates that this date is before the given one, and a positive date indicates that it is after. */\n  compare(b: CalendarDate | CalendarDateTime | ZonedDateTime): number {\n    let res = compareDate(this, b);\n    if (res === 0) {\n      return compareTime(this, toCalendarDateTime(b));\n    }\n\n    return res;\n  }\n}\n\n/** A ZonedDateTime represents a date and time in a specific time zone and calendar system. */\nexport class ZonedDateTime {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // @ts-ignore\n  #type;\n  /** The calendar system associated with this date, e.g. Gregorian. */\n  public readonly calendar: Calendar;\n  /** The calendar era for this date, e.g. \"BC\" or \"AD\". */\n  public readonly era: string;\n  /** The year of this date within the era. */\n  public readonly year: number;\n  /**\n   * The month number within the year. Note that some calendar systems such as Hebrew\n   * may have a variable number of months per year. Therefore, month numbers may not\n   * always correspond to the same month names in different years.\n   */\n  public readonly month: number;\n  /** The day number within the month. */\n  public readonly day: number;\n  /** The hour in the day, numbered from 0 to 23. */\n  public readonly hour: number;\n  /** The minute in the hour. */\n  public readonly minute: number;\n  /** The second in the minute. */\n  public readonly second: number;\n  /** The millisecond in the second. */\n  public readonly millisecond: number;\n  /** The IANA time zone identifier that this date and time is represented in. */\n  public readonly timeZone: string;\n  /** The UTC offset for this time, in milliseconds. */\n  public readonly offset: number;\n\n  constructor(year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(era: string, year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, era: string, year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(...args: any[]) {\n    let [calendar, era, year, month, day] = shiftArgs(args);\n    let timeZone = args.shift();\n    let offset = args.shift();\n    this.calendar = calendar;\n    this.era = era;\n    this.year = year;\n    this.month = month;\n    this.day = day;\n    this.timeZone = timeZone;\n    this.offset = offset;\n    this.hour = args.shift() || 0;\n    this.minute = args.shift() || 0;\n    this.second = args.shift() || 0;\n    this.millisecond = args.shift() || 0;\n\n    constrain(this);\n  }\n\n  /** Returns a copy of this date. */\n  copy(): ZonedDateTime {\n    if (this.era) {\n      return new ZonedDateTime(this.calendar, this.era, this.year, this.month, this.day, this.timeZone, this.offset, this.hour, this.minute, this.second, this.millisecond);\n    } else {\n      return new ZonedDateTime(this.calendar, this.year, this.month, this.day, this.timeZone, this.offset, this.hour, this.minute, this.second, this.millisecond);\n    }\n  }\n\n  /** Returns a new `ZonedDateTime` with the given duration added to it. */\n  add(duration: DateTimeDuration) {\n    return addZoned(this, duration);\n  }\n\n  /** Returns a new `ZonedDateTime` with the given duration subtracted from it. */\n  subtract(duration: DateTimeDuration) {\n    return subtractZoned(this, duration);\n  }\n\n  /** Returns a new `ZonedDateTime` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: DateFields & TimeFields, disambiguation?: Disambiguation) {\n    return setZoned(this, fields, disambiguation);\n  }\n\n  /**\n   * Returns a new `ZonedDateTime` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: DateField | TimeField, amount: number, options?: CycleTimeOptions) {\n    return cycleZoned(this, field, amount, options);\n  }\n\n  /** Converts the date to a native JavaScript Date object. */\n  toDate() {\n    return zonedToDate(this);\n  }\n\n   /** Converts the date to an ISO 8601 formatted string, including the UTC offset and time zone identifier. */\n  toString() {\n    return zonedDateTimeToString(this);\n  }\n\n   /** Converts the date to an ISO 8601 formatted string in UTC. */\n  toAbsoluteString() {\n    return this.toDate().toISOString();\n  }\n\n  /** Compares this date with another. A negative result indicates that this date is before the given one, and a positive date indicates that it is after. */\n  compare(b: CalendarDate | CalendarDateTime | ZonedDateTime) {\n    // TODO: Is this a bad idea??\n    return this.toDate().getTime() - toZoned(b, this.timeZone).toDate().getTime();\n  }\n}\n"], "names": [], "version": 3, "file": "CalendarDate.module.js.map"}