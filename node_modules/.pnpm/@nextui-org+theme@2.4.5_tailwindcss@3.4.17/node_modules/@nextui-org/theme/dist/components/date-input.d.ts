import * as tailwind_variants from 'tailwind-variants';
import { VariantProps } from 'tailwind-variants';
import * as tailwind_variants_dist_config from 'tailwind-variants/dist/config';

/**
 * DateInput wrapper **Tailwind Variants** component
 *
 * @example
 */
declare const dateInput: tailwind_variants.TVReturnType<{
    variant: {
        flat: {
            inputWrapper: string[];
        };
        faded: {
            inputWrapper: string[];
        };
        bordered: {
            inputWrapper: string[];
        };
        underlined: {
            inputWrapper: string[];
        };
    };
    color: {
        default: {
            segment: string;
        };
        primary: {
            segment: string;
        };
        secondary: {
            segment: string;
        };
        success: {
            segment: string;
        };
        warning: {
            segment: string;
        };
        danger: {
            segment: string;
        };
    };
    size: {
        sm: {
            label: string;
            input: string;
            inputWrapper: string;
        };
        md: {
            input: string;
            inputWrapper: string;
            clearButton: string;
        };
        lg: {
            label: string;
            input: string;
            inputWrapper: string;
        };
    };
    radius: {
        none: {
            inputWrapper: string;
        };
        sm: {
            inputWrapper: string;
        };
        md: {
            inputWrapper: string;
        };
        lg: {
            inputWrapper: string;
        };
        full: {
            inputWrapper: string;
        };
    };
    labelPlacement: {
        outside: {
            base: string;
            label: string;
            helperWrapper: string;
        };
        "outside-left": {
            base: string;
            label: string;
            inputWrapper: string;
            helperWrapper: string;
        };
        inside: {
            label: string;
            inputWrapper: string;
        };
    };
    fullWidth: {
        true: {
            base: string;
            inputWrapper: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
            inputWrapper: string;
            label: string;
        };
    };
    disableAnimation: {
        true: {
            label: string;
            input: string;
            inputWrapper: string;
        };
        false: {
            label: string[];
            inputWrapper: string;
            segment: string;
        };
    };
}, {
    base: string;
    label: string[];
    inputWrapper: string[];
    input: string;
    innerWrapper: string[];
    segment: string[];
    helperWrapper: string;
    description: string;
    errorMessage: string;
}, undefined, tailwind_variants_dist_config.TVConfig<{
    variant: {
        flat: {
            inputWrapper: string[];
        };
        faded: {
            inputWrapper: string[];
        };
        bordered: {
            inputWrapper: string[];
        };
        underlined: {
            inputWrapper: string[];
        };
    };
    color: {
        default: {
            segment: string;
        };
        primary: {
            segment: string;
        };
        secondary: {
            segment: string;
        };
        success: {
            segment: string;
        };
        warning: {
            segment: string;
        };
        danger: {
            segment: string;
        };
    };
    size: {
        sm: {
            label: string;
            input: string;
            inputWrapper: string;
        };
        md: {
            input: string;
            inputWrapper: string;
            clearButton: string;
        };
        lg: {
            label: string;
            input: string;
            inputWrapper: string;
        };
    };
    radius: {
        none: {
            inputWrapper: string;
        };
        sm: {
            inputWrapper: string;
        };
        md: {
            inputWrapper: string;
        };
        lg: {
            inputWrapper: string;
        };
        full: {
            inputWrapper: string;
        };
    };
    labelPlacement: {
        outside: {
            base: string;
            label: string;
            helperWrapper: string;
        };
        "outside-left": {
            base: string;
            label: string;
            inputWrapper: string;
            helperWrapper: string;
        };
        inside: {
            label: string;
            inputWrapper: string;
        };
    };
    fullWidth: {
        true: {
            base: string;
            inputWrapper: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
            inputWrapper: string;
            label: string;
        };
    };
    disableAnimation: {
        true: {
            label: string;
            input: string;
            inputWrapper: string;
        };
        false: {
            label: string[];
            inputWrapper: string;
            segment: string;
        };
    };
}, {
    variant: {
        flat: {
            inputWrapper: string[];
        };
        faded: {
            inputWrapper: string[];
        };
        bordered: {
            inputWrapper: string[];
        };
        underlined: {
            inputWrapper: string[];
        };
    };
    color: {
        default: {
            segment: string;
        };
        primary: {
            segment: string;
        };
        secondary: {
            segment: string;
        };
        success: {
            segment: string;
        };
        warning: {
            segment: string;
        };
        danger: {
            segment: string;
        };
    };
    size: {
        sm: {
            label: string;
            input: string;
            inputWrapper: string;
        };
        md: {
            input: string;
            inputWrapper: string;
            clearButton: string;
        };
        lg: {
            label: string;
            input: string;
            inputWrapper: string;
        };
    };
    radius: {
        none: {
            inputWrapper: string;
        };
        sm: {
            inputWrapper: string;
        };
        md: {
            inputWrapper: string;
        };
        lg: {
            inputWrapper: string;
        };
        full: {
            inputWrapper: string;
        };
    };
    labelPlacement: {
        outside: {
            base: string;
            label: string;
            helperWrapper: string;
        };
        "outside-left": {
            base: string;
            label: string;
            inputWrapper: string;
            helperWrapper: string;
        };
        inside: {
            label: string;
            inputWrapper: string;
        };
    };
    fullWidth: {
        true: {
            base: string;
            inputWrapper: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
            inputWrapper: string;
            label: string;
        };
    };
    disableAnimation: {
        true: {
            label: string;
            input: string;
            inputWrapper: string;
        };
        false: {
            label: string[];
            inputWrapper: string;
            segment: string;
        };
    };
}>, {
    variant: {
        flat: {
            inputWrapper: string[];
        };
        faded: {
            inputWrapper: string[];
        };
        bordered: {
            inputWrapper: string[];
        };
        underlined: {
            inputWrapper: string[];
        };
    };
    color: {
        default: {
            segment: string;
        };
        primary: {
            segment: string;
        };
        secondary: {
            segment: string;
        };
        success: {
            segment: string;
        };
        warning: {
            segment: string;
        };
        danger: {
            segment: string;
        };
    };
    size: {
        sm: {
            label: string;
            input: string;
            inputWrapper: string;
        };
        md: {
            input: string;
            inputWrapper: string;
            clearButton: string;
        };
        lg: {
            label: string;
            input: string;
            inputWrapper: string;
        };
    };
    radius: {
        none: {
            inputWrapper: string;
        };
        sm: {
            inputWrapper: string;
        };
        md: {
            inputWrapper: string;
        };
        lg: {
            inputWrapper: string;
        };
        full: {
            inputWrapper: string;
        };
    };
    labelPlacement: {
        outside: {
            base: string;
            label: string;
            helperWrapper: string;
        };
        "outside-left": {
            base: string;
            label: string;
            inputWrapper: string;
            helperWrapper: string;
        };
        inside: {
            label: string;
            inputWrapper: string;
        };
    };
    fullWidth: {
        true: {
            base: string;
            inputWrapper: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
            inputWrapper: string;
            label: string;
        };
    };
    disableAnimation: {
        true: {
            label: string;
            input: string;
            inputWrapper: string;
        };
        false: {
            label: string[];
            inputWrapper: string;
            segment: string;
        };
    };
}, {
    base: string;
    label: string[];
    inputWrapper: string[];
    input: string;
    innerWrapper: string[];
    segment: string[];
    helperWrapper: string;
    description: string;
    errorMessage: string;
}, tailwind_variants.TVReturnType<{
    variant: {
        flat: {
            inputWrapper: string[];
        };
        faded: {
            inputWrapper: string[];
        };
        bordered: {
            inputWrapper: string[];
        };
        underlined: {
            inputWrapper: string[];
        };
    };
    color: {
        default: {
            segment: string;
        };
        primary: {
            segment: string;
        };
        secondary: {
            segment: string;
        };
        success: {
            segment: string;
        };
        warning: {
            segment: string;
        };
        danger: {
            segment: string;
        };
    };
    size: {
        sm: {
            label: string;
            input: string;
            inputWrapper: string;
        };
        md: {
            input: string;
            inputWrapper: string;
            clearButton: string;
        };
        lg: {
            label: string;
            input: string;
            inputWrapper: string;
        };
    };
    radius: {
        none: {
            inputWrapper: string;
        };
        sm: {
            inputWrapper: string;
        };
        md: {
            inputWrapper: string;
        };
        lg: {
            inputWrapper: string;
        };
        full: {
            inputWrapper: string;
        };
    };
    labelPlacement: {
        outside: {
            base: string;
            label: string;
            helperWrapper: string;
        };
        "outside-left": {
            base: string;
            label: string;
            inputWrapper: string;
            helperWrapper: string;
        };
        inside: {
            label: string;
            inputWrapper: string;
        };
    };
    fullWidth: {
        true: {
            base: string;
            inputWrapper: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
            inputWrapper: string;
            label: string;
        };
    };
    disableAnimation: {
        true: {
            label: string;
            input: string;
            inputWrapper: string;
        };
        false: {
            label: string[];
            inputWrapper: string;
            segment: string;
        };
    };
}, {
    base: string;
    label: string[];
    inputWrapper: string[];
    input: string;
    innerWrapper: string[];
    segment: string[];
    helperWrapper: string;
    description: string;
    errorMessage: string;
}, undefined, tailwind_variants_dist_config.TVConfig<{
    variant: {
        flat: {
            inputWrapper: string[];
        };
        faded: {
            inputWrapper: string[];
        };
        bordered: {
            inputWrapper: string[];
        };
        underlined: {
            inputWrapper: string[];
        };
    };
    color: {
        default: {
            segment: string;
        };
        primary: {
            segment: string;
        };
        secondary: {
            segment: string;
        };
        success: {
            segment: string;
        };
        warning: {
            segment: string;
        };
        danger: {
            segment: string;
        };
    };
    size: {
        sm: {
            label: string;
            input: string;
            inputWrapper: string;
        };
        md: {
            input: string;
            inputWrapper: string;
            clearButton: string;
        };
        lg: {
            label: string;
            input: string;
            inputWrapper: string;
        };
    };
    radius: {
        none: {
            inputWrapper: string;
        };
        sm: {
            inputWrapper: string;
        };
        md: {
            inputWrapper: string;
        };
        lg: {
            inputWrapper: string;
        };
        full: {
            inputWrapper: string;
        };
    };
    labelPlacement: {
        outside: {
            base: string;
            label: string;
            helperWrapper: string;
        };
        "outside-left": {
            base: string;
            label: string;
            inputWrapper: string;
            helperWrapper: string;
        };
        inside: {
            label: string;
            inputWrapper: string;
        };
    };
    fullWidth: {
        true: {
            base: string;
            inputWrapper: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
            inputWrapper: string;
            label: string;
        };
    };
    disableAnimation: {
        true: {
            label: string;
            input: string;
            inputWrapper: string;
        };
        false: {
            label: string[];
            inputWrapper: string;
            segment: string;
        };
    };
}, {
    variant: {
        flat: {
            inputWrapper: string[];
        };
        faded: {
            inputWrapper: string[];
        };
        bordered: {
            inputWrapper: string[];
        };
        underlined: {
            inputWrapper: string[];
        };
    };
    color: {
        default: {
            segment: string;
        };
        primary: {
            segment: string;
        };
        secondary: {
            segment: string;
        };
        success: {
            segment: string;
        };
        warning: {
            segment: string;
        };
        danger: {
            segment: string;
        };
    };
    size: {
        sm: {
            label: string;
            input: string;
            inputWrapper: string;
        };
        md: {
            input: string;
            inputWrapper: string;
            clearButton: string;
        };
        lg: {
            label: string;
            input: string;
            inputWrapper: string;
        };
    };
    radius: {
        none: {
            inputWrapper: string;
        };
        sm: {
            inputWrapper: string;
        };
        md: {
            inputWrapper: string;
        };
        lg: {
            inputWrapper: string;
        };
        full: {
            inputWrapper: string;
        };
    };
    labelPlacement: {
        outside: {
            base: string;
            label: string;
            helperWrapper: string;
        };
        "outside-left": {
            base: string;
            label: string;
            inputWrapper: string;
            helperWrapper: string;
        };
        inside: {
            label: string;
            inputWrapper: string;
        };
    };
    fullWidth: {
        true: {
            base: string;
            inputWrapper: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
            inputWrapper: string;
            label: string;
        };
    };
    disableAnimation: {
        true: {
            label: string;
            input: string;
            inputWrapper: string;
        };
        false: {
            label: string[];
            inputWrapper: string;
            segment: string;
        };
    };
}>, unknown, unknown, undefined>>;
type DateInputReturnType = ReturnType<typeof dateInput>;
type DateInputVariantProps = VariantProps<typeof dateInput>;
type DateInputSlots = keyof ReturnType<typeof dateInput>;

export { DateInputReturnType, DateInputSlots, DateInputVariantProps, dateInput };
