import * as tailwind_variants from 'tailwind-variants';
import { VariantProps } from 'tailwind-variants';
import * as tailwind_variants_dist_config from 'tailwind-variants/dist/config';

declare const select: tailwind_variants.TVReturnType<{
    variant: {
        flat: {
            trigger: string[];
        };
        faded: {
            trigger: string[];
            value: string;
        };
        bordered: {
            trigger: string[];
            value: string;
        };
        underlined: {
            trigger: string[];
            value: string;
        };
    };
    color: {
        default: {};
        primary: {
            selectorIcon: string;
        };
        secondary: {
            selectorIcon: string;
        };
        success: {
            selectorIcon: string;
        };
        warning: {
            selectorIcon: string;
        };
        danger: {
            selectorIcon: string;
        };
    };
    size: {
        sm: {
            label: string;
            trigger: string;
            value: string;
        };
        md: {
            trigger: string;
            value: string;
        };
        lg: {
            trigger: string;
            value: string;
        };
    };
    radius: {
        none: {
            trigger: string;
        };
        sm: {
            trigger: string;
        };
        md: {
            trigger: string;
        };
        lg: {
            trigger: string;
        };
        full: {
            trigger: string;
        };
    };
    labelPlacement: {
        outside: {
            base: string;
        };
        "outside-left": {
            base: string;
            label: string;
        };
        inside: {
            label: string;
            trigger: string;
        };
    };
    fullWidth: {
        true: {
            base: string;
        };
        false: {
            base: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
            trigger: string;
        };
    };
    isInvalid: {
        true: {
            label: string;
            value: string;
            selectorIcon: string;
        };
    };
    isRequired: {
        true: {
            label: string;
        };
    };
    isMultiline: {
        true: {
            label: string;
            trigger: string;
        };
        false: {
            value: string;
        };
    };
    disableAnimation: {
        true: {
            trigger: string;
            base: string;
            label: string;
            selectorIcon: string;
        };
        false: {
            base: string;
            label: string[];
            selectorIcon: string;
        };
    };
    disableSelectorIconRotation: {
        true: {};
        false: {
            selectorIcon: string;
        };
    };
}, {
    base: string[];
    label: string[];
    mainWrapper: string;
    trigger: string;
    innerWrapper: string;
    selectorIcon: string;
    spinner: string;
    value: string[];
    listboxWrapper: string;
    listbox: string;
    popoverContent: string;
    helperWrapper: string;
    description: string;
    errorMessage: string;
}, undefined, tailwind_variants_dist_config.TVConfig<{
    variant: {
        flat: {
            trigger: string[];
        };
        faded: {
            trigger: string[];
            value: string;
        };
        bordered: {
            trigger: string[];
            value: string;
        };
        underlined: {
            trigger: string[];
            value: string;
        };
    };
    color: {
        default: {};
        primary: {
            selectorIcon: string;
        };
        secondary: {
            selectorIcon: string;
        };
        success: {
            selectorIcon: string;
        };
        warning: {
            selectorIcon: string;
        };
        danger: {
            selectorIcon: string;
        };
    };
    size: {
        sm: {
            label: string;
            trigger: string;
            value: string;
        };
        md: {
            trigger: string;
            value: string;
        };
        lg: {
            trigger: string;
            value: string;
        };
    };
    radius: {
        none: {
            trigger: string;
        };
        sm: {
            trigger: string;
        };
        md: {
            trigger: string;
        };
        lg: {
            trigger: string;
        };
        full: {
            trigger: string;
        };
    };
    labelPlacement: {
        outside: {
            base: string;
        };
        "outside-left": {
            base: string;
            label: string;
        };
        inside: {
            label: string;
            trigger: string;
        };
    };
    fullWidth: {
        true: {
            base: string;
        };
        false: {
            base: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
            trigger: string;
        };
    };
    isInvalid: {
        true: {
            label: string;
            value: string;
            selectorIcon: string;
        };
    };
    isRequired: {
        true: {
            label: string;
        };
    };
    isMultiline: {
        true: {
            label: string;
            trigger: string;
        };
        false: {
            value: string;
        };
    };
    disableAnimation: {
        true: {
            trigger: string;
            base: string;
            label: string;
            selectorIcon: string;
        };
        false: {
            base: string;
            label: string[];
            selectorIcon: string;
        };
    };
    disableSelectorIconRotation: {
        true: {};
        false: {
            selectorIcon: string;
        };
    };
}, {
    variant: {
        flat: {
            trigger: string[];
        };
        faded: {
            trigger: string[];
            value: string;
        };
        bordered: {
            trigger: string[];
            value: string;
        };
        underlined: {
            trigger: string[];
            value: string;
        };
    };
    color: {
        default: {};
        primary: {
            selectorIcon: string;
        };
        secondary: {
            selectorIcon: string;
        };
        success: {
            selectorIcon: string;
        };
        warning: {
            selectorIcon: string;
        };
        danger: {
            selectorIcon: string;
        };
    };
    size: {
        sm: {
            label: string;
            trigger: string;
            value: string;
        };
        md: {
            trigger: string;
            value: string;
        };
        lg: {
            trigger: string;
            value: string;
        };
    };
    radius: {
        none: {
            trigger: string;
        };
        sm: {
            trigger: string;
        };
        md: {
            trigger: string;
        };
        lg: {
            trigger: string;
        };
        full: {
            trigger: string;
        };
    };
    labelPlacement: {
        outside: {
            base: string;
        };
        "outside-left": {
            base: string;
            label: string;
        };
        inside: {
            label: string;
            trigger: string;
        };
    };
    fullWidth: {
        true: {
            base: string;
        };
        false: {
            base: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
            trigger: string;
        };
    };
    isInvalid: {
        true: {
            label: string;
            value: string;
            selectorIcon: string;
        };
    };
    isRequired: {
        true: {
            label: string;
        };
    };
    isMultiline: {
        true: {
            label: string;
            trigger: string;
        };
        false: {
            value: string;
        };
    };
    disableAnimation: {
        true: {
            trigger: string;
            base: string;
            label: string;
            selectorIcon: string;
        };
        false: {
            base: string;
            label: string[];
            selectorIcon: string;
        };
    };
    disableSelectorIconRotation: {
        true: {};
        false: {
            selectorIcon: string;
        };
    };
}>, {
    variant: {
        flat: {
            trigger: string[];
        };
        faded: {
            trigger: string[];
            value: string;
        };
        bordered: {
            trigger: string[];
            value: string;
        };
        underlined: {
            trigger: string[];
            value: string;
        };
    };
    color: {
        default: {};
        primary: {
            selectorIcon: string;
        };
        secondary: {
            selectorIcon: string;
        };
        success: {
            selectorIcon: string;
        };
        warning: {
            selectorIcon: string;
        };
        danger: {
            selectorIcon: string;
        };
    };
    size: {
        sm: {
            label: string;
            trigger: string;
            value: string;
        };
        md: {
            trigger: string;
            value: string;
        };
        lg: {
            trigger: string;
            value: string;
        };
    };
    radius: {
        none: {
            trigger: string;
        };
        sm: {
            trigger: string;
        };
        md: {
            trigger: string;
        };
        lg: {
            trigger: string;
        };
        full: {
            trigger: string;
        };
    };
    labelPlacement: {
        outside: {
            base: string;
        };
        "outside-left": {
            base: string;
            label: string;
        };
        inside: {
            label: string;
            trigger: string;
        };
    };
    fullWidth: {
        true: {
            base: string;
        };
        false: {
            base: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
            trigger: string;
        };
    };
    isInvalid: {
        true: {
            label: string;
            value: string;
            selectorIcon: string;
        };
    };
    isRequired: {
        true: {
            label: string;
        };
    };
    isMultiline: {
        true: {
            label: string;
            trigger: string;
        };
        false: {
            value: string;
        };
    };
    disableAnimation: {
        true: {
            trigger: string;
            base: string;
            label: string;
            selectorIcon: string;
        };
        false: {
            base: string;
            label: string[];
            selectorIcon: string;
        };
    };
    disableSelectorIconRotation: {
        true: {};
        false: {
            selectorIcon: string;
        };
    };
}, {
    base: string[];
    label: string[];
    mainWrapper: string;
    trigger: string;
    innerWrapper: string;
    selectorIcon: string;
    spinner: string;
    value: string[];
    listboxWrapper: string;
    listbox: string;
    popoverContent: string;
    helperWrapper: string;
    description: string;
    errorMessage: string;
}, tailwind_variants.TVReturnType<{
    variant: {
        flat: {
            trigger: string[];
        };
        faded: {
            trigger: string[];
            value: string;
        };
        bordered: {
            trigger: string[];
            value: string;
        };
        underlined: {
            trigger: string[];
            value: string;
        };
    };
    color: {
        default: {};
        primary: {
            selectorIcon: string;
        };
        secondary: {
            selectorIcon: string;
        };
        success: {
            selectorIcon: string;
        };
        warning: {
            selectorIcon: string;
        };
        danger: {
            selectorIcon: string;
        };
    };
    size: {
        sm: {
            label: string;
            trigger: string;
            value: string;
        };
        md: {
            trigger: string;
            value: string;
        };
        lg: {
            trigger: string;
            value: string;
        };
    };
    radius: {
        none: {
            trigger: string;
        };
        sm: {
            trigger: string;
        };
        md: {
            trigger: string;
        };
        lg: {
            trigger: string;
        };
        full: {
            trigger: string;
        };
    };
    labelPlacement: {
        outside: {
            base: string;
        };
        "outside-left": {
            base: string;
            label: string;
        };
        inside: {
            label: string;
            trigger: string;
        };
    };
    fullWidth: {
        true: {
            base: string;
        };
        false: {
            base: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
            trigger: string;
        };
    };
    isInvalid: {
        true: {
            label: string;
            value: string;
            selectorIcon: string;
        };
    };
    isRequired: {
        true: {
            label: string;
        };
    };
    isMultiline: {
        true: {
            label: string;
            trigger: string;
        };
        false: {
            value: string;
        };
    };
    disableAnimation: {
        true: {
            trigger: string;
            base: string;
            label: string;
            selectorIcon: string;
        };
        false: {
            base: string;
            label: string[];
            selectorIcon: string;
        };
    };
    disableSelectorIconRotation: {
        true: {};
        false: {
            selectorIcon: string;
        };
    };
}, {
    base: string[];
    label: string[];
    mainWrapper: string;
    trigger: string;
    innerWrapper: string;
    selectorIcon: string;
    spinner: string;
    value: string[];
    listboxWrapper: string;
    listbox: string;
    popoverContent: string;
    helperWrapper: string;
    description: string;
    errorMessage: string;
}, undefined, tailwind_variants_dist_config.TVConfig<{
    variant: {
        flat: {
            trigger: string[];
        };
        faded: {
            trigger: string[];
            value: string;
        };
        bordered: {
            trigger: string[];
            value: string;
        };
        underlined: {
            trigger: string[];
            value: string;
        };
    };
    color: {
        default: {};
        primary: {
            selectorIcon: string;
        };
        secondary: {
            selectorIcon: string;
        };
        success: {
            selectorIcon: string;
        };
        warning: {
            selectorIcon: string;
        };
        danger: {
            selectorIcon: string;
        };
    };
    size: {
        sm: {
            label: string;
            trigger: string;
            value: string;
        };
        md: {
            trigger: string;
            value: string;
        };
        lg: {
            trigger: string;
            value: string;
        };
    };
    radius: {
        none: {
            trigger: string;
        };
        sm: {
            trigger: string;
        };
        md: {
            trigger: string;
        };
        lg: {
            trigger: string;
        };
        full: {
            trigger: string;
        };
    };
    labelPlacement: {
        outside: {
            base: string;
        };
        "outside-left": {
            base: string;
            label: string;
        };
        inside: {
            label: string;
            trigger: string;
        };
    };
    fullWidth: {
        true: {
            base: string;
        };
        false: {
            base: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
            trigger: string;
        };
    };
    isInvalid: {
        true: {
            label: string;
            value: string;
            selectorIcon: string;
        };
    };
    isRequired: {
        true: {
            label: string;
        };
    };
    isMultiline: {
        true: {
            label: string;
            trigger: string;
        };
        false: {
            value: string;
        };
    };
    disableAnimation: {
        true: {
            trigger: string;
            base: string;
            label: string;
            selectorIcon: string;
        };
        false: {
            base: string;
            label: string[];
            selectorIcon: string;
        };
    };
    disableSelectorIconRotation: {
        true: {};
        false: {
            selectorIcon: string;
        };
    };
}, {
    variant: {
        flat: {
            trigger: string[];
        };
        faded: {
            trigger: string[];
            value: string;
        };
        bordered: {
            trigger: string[];
            value: string;
        };
        underlined: {
            trigger: string[];
            value: string;
        };
    };
    color: {
        default: {};
        primary: {
            selectorIcon: string;
        };
        secondary: {
            selectorIcon: string;
        };
        success: {
            selectorIcon: string;
        };
        warning: {
            selectorIcon: string;
        };
        danger: {
            selectorIcon: string;
        };
    };
    size: {
        sm: {
            label: string;
            trigger: string;
            value: string;
        };
        md: {
            trigger: string;
            value: string;
        };
        lg: {
            trigger: string;
            value: string;
        };
    };
    radius: {
        none: {
            trigger: string;
        };
        sm: {
            trigger: string;
        };
        md: {
            trigger: string;
        };
        lg: {
            trigger: string;
        };
        full: {
            trigger: string;
        };
    };
    labelPlacement: {
        outside: {
            base: string;
        };
        "outside-left": {
            base: string;
            label: string;
        };
        inside: {
            label: string;
            trigger: string;
        };
    };
    fullWidth: {
        true: {
            base: string;
        };
        false: {
            base: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
            trigger: string;
        };
    };
    isInvalid: {
        true: {
            label: string;
            value: string;
            selectorIcon: string;
        };
    };
    isRequired: {
        true: {
            label: string;
        };
    };
    isMultiline: {
        true: {
            label: string;
            trigger: string;
        };
        false: {
            value: string;
        };
    };
    disableAnimation: {
        true: {
            trigger: string;
            base: string;
            label: string;
            selectorIcon: string;
        };
        false: {
            base: string;
            label: string[];
            selectorIcon: string;
        };
    };
    disableSelectorIconRotation: {
        true: {};
        false: {
            selectorIcon: string;
        };
    };
}>, unknown, unknown, undefined>>;
type SelectVariantProps = VariantProps<typeof select>;
type SelectSlots = keyof ReturnType<typeof select>;

export { SelectSlots, SelectVariantProps, select };
