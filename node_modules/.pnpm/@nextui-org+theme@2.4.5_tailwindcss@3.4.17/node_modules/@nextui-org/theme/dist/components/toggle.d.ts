import * as tailwind_variants from 'tailwind-variants';
import { VariantProps } from 'tailwind-variants';
import * as tailwind_variants_dist_config from 'tailwind-variants/dist/config';

/**
 * Toggle (Switch) wrapper **Tailwind Variants** component
 *
 * const {base, wrapper, thumb, thumbIcon, label, startContent, endContent} = toggle({...})
 *
 * @example
 * <label
 *    className={base())}
 *    data-selected={true/false}
 *    data-pressed={true/false}
 *    data-focus={true/false}
 *    data-hover={true/false}
 *    data-focus-visible={true/false}
 * >
 *  <input/> // hidden input
 *  <span className={wrapper()} aria-hidden="true">
 *    <svg className={startContent()}>...</svg>
 *    <span className={thumb()}>
 *      <svg className={thumbIcon()}>...</svg>
 *    </span>
 *    <svg className={endContent()}>...</svg>
 *  </span>
 *  <span className={label()}>Label</span>
 * </label>
 */
declare const toggle: tailwind_variants.TVReturnType<{
    color: {
        default: {
            wrapper: string[];
        };
        primary: {
            wrapper: string[];
        };
        secondary: {
            wrapper: string[];
        };
        success: {
            wrapper: string[];
        };
        warning: {
            wrapper: string[];
        };
        danger: {
            wrapper: string[];
        };
    };
    size: {
        sm: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
        md: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
        lg: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
        };
    };
    disableAnimation: {
        true: {
            wrapper: string;
            thumb: string;
        };
        false: {
            wrapper: string;
            thumb: string;
            startContent: string[];
            endContent: string[];
        };
    };
}, {
    base: string;
    wrapper: string[];
    thumb: string[];
    hiddenInput: string[];
    startContent: string;
    endContent: string;
    thumbIcon: string;
    label: string;
}, undefined, tailwind_variants_dist_config.TVConfig<{
    color: {
        default: {
            wrapper: string[];
        };
        primary: {
            wrapper: string[];
        };
        secondary: {
            wrapper: string[];
        };
        success: {
            wrapper: string[];
        };
        warning: {
            wrapper: string[];
        };
        danger: {
            wrapper: string[];
        };
    };
    size: {
        sm: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
        md: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
        lg: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
        };
    };
    disableAnimation: {
        true: {
            wrapper: string;
            thumb: string;
        };
        false: {
            wrapper: string;
            thumb: string;
            startContent: string[];
            endContent: string[];
        };
    };
}, {
    color: {
        default: {
            wrapper: string[];
        };
        primary: {
            wrapper: string[];
        };
        secondary: {
            wrapper: string[];
        };
        success: {
            wrapper: string[];
        };
        warning: {
            wrapper: string[];
        };
        danger: {
            wrapper: string[];
        };
    };
    size: {
        sm: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
        md: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
        lg: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
        };
    };
    disableAnimation: {
        true: {
            wrapper: string;
            thumb: string;
        };
        false: {
            wrapper: string;
            thumb: string;
            startContent: string[];
            endContent: string[];
        };
    };
}>, {
    color: {
        default: {
            wrapper: string[];
        };
        primary: {
            wrapper: string[];
        };
        secondary: {
            wrapper: string[];
        };
        success: {
            wrapper: string[];
        };
        warning: {
            wrapper: string[];
        };
        danger: {
            wrapper: string[];
        };
    };
    size: {
        sm: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
        md: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
        lg: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
        };
    };
    disableAnimation: {
        true: {
            wrapper: string;
            thumb: string;
        };
        false: {
            wrapper: string;
            thumb: string;
            startContent: string[];
            endContent: string[];
        };
    };
}, {
    base: string;
    wrapper: string[];
    thumb: string[];
    hiddenInput: string[];
    startContent: string;
    endContent: string;
    thumbIcon: string;
    label: string;
}, tailwind_variants.TVReturnType<{
    color: {
        default: {
            wrapper: string[];
        };
        primary: {
            wrapper: string[];
        };
        secondary: {
            wrapper: string[];
        };
        success: {
            wrapper: string[];
        };
        warning: {
            wrapper: string[];
        };
        danger: {
            wrapper: string[];
        };
    };
    size: {
        sm: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
        md: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
        lg: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
        };
    };
    disableAnimation: {
        true: {
            wrapper: string;
            thumb: string;
        };
        false: {
            wrapper: string;
            thumb: string;
            startContent: string[];
            endContent: string[];
        };
    };
}, {
    base: string;
    wrapper: string[];
    thumb: string[];
    hiddenInput: string[];
    startContent: string;
    endContent: string;
    thumbIcon: string;
    label: string;
}, undefined, tailwind_variants_dist_config.TVConfig<{
    color: {
        default: {
            wrapper: string[];
        };
        primary: {
            wrapper: string[];
        };
        secondary: {
            wrapper: string[];
        };
        success: {
            wrapper: string[];
        };
        warning: {
            wrapper: string[];
        };
        danger: {
            wrapper: string[];
        };
    };
    size: {
        sm: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
        md: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
        lg: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
        };
    };
    disableAnimation: {
        true: {
            wrapper: string;
            thumb: string;
        };
        false: {
            wrapper: string;
            thumb: string;
            startContent: string[];
            endContent: string[];
        };
    };
}, {
    color: {
        default: {
            wrapper: string[];
        };
        primary: {
            wrapper: string[];
        };
        secondary: {
            wrapper: string[];
        };
        success: {
            wrapper: string[];
        };
        warning: {
            wrapper: string[];
        };
        danger: {
            wrapper: string[];
        };
    };
    size: {
        sm: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
        md: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
        lg: {
            wrapper: string;
            thumb: string[];
            endContent: string;
            startContent: string;
            label: string;
        };
    };
    isDisabled: {
        true: {
            base: string;
        };
    };
    disableAnimation: {
        true: {
            wrapper: string;
            thumb: string;
        };
        false: {
            wrapper: string;
            thumb: string;
            startContent: string[];
            endContent: string[];
        };
    };
}>, unknown, unknown, undefined>>;
type ToggleVariantProps = VariantProps<typeof toggle>;
type ToggleSlots = keyof ReturnType<typeof toggle>;

export { ToggleSlots, ToggleVariantProps, toggle };
