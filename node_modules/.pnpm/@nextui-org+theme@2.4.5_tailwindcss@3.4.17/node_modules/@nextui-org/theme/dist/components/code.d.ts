import * as tailwind_variants from 'tailwind-variants';
import { VariantProps } from 'tailwind-variants';
import * as tailwind_variants_dist_config from 'tailwind-variants/dist/config';

/**
 * Code wrapper **Tailwind Variants** component
 *
 * const classNames = code({...})
 *
 * @example
 * <code className={classNames)}>
 *   npm install @nextui-org/react
 * </code>
 */
declare const code: tailwind_variants.TVReturnType<{
    color: {
        default: string;
        primary: string;
        secondary: string;
        success: string;
        warning: string;
        danger: string;
    };
    size: {
        sm: "text-small";
        md: "text-medium";
        lg: "text-large";
    };
    radius: {
        none: "rounded-none";
        sm: "rounded-small";
        md: "rounded-medium";
        lg: "rounded-large";
        full: "rounded-full";
    };
}, undefined, string[], tailwind_variants_dist_config.TVConfig<{
    color: {
        default: string;
        primary: string;
        secondary: string;
        success: string;
        warning: string;
        danger: string;
    };
    size: {
        sm: "text-small";
        md: "text-medium";
        lg: "text-large";
    };
    radius: {
        none: "rounded-none";
        sm: "rounded-small";
        md: "rounded-medium";
        lg: "rounded-large";
        full: "rounded-full";
    };
}, {
    color: {
        default: string;
        primary: string;
        secondary: string;
        success: string;
        warning: string;
        danger: string;
    };
    size: {
        sm: "text-small";
        md: "text-medium";
        lg: "text-large";
    };
    radius: {
        none: "rounded-none";
        sm: "rounded-small";
        md: "rounded-medium";
        lg: "rounded-large";
        full: "rounded-full";
    };
}>, {
    color: {
        default: string;
        primary: string;
        secondary: string;
        success: string;
        warning: string;
        danger: string;
    };
    size: {
        sm: "text-small";
        md: "text-medium";
        lg: "text-large";
    };
    radius: {
        none: "rounded-none";
        sm: "rounded-small";
        md: "rounded-medium";
        lg: "rounded-large";
        full: "rounded-full";
    };
}, undefined, tailwind_variants.TVReturnType<{
    color: {
        default: string;
        primary: string;
        secondary: string;
        success: string;
        warning: string;
        danger: string;
    };
    size: {
        sm: "text-small";
        md: "text-medium";
        lg: "text-large";
    };
    radius: {
        none: "rounded-none";
        sm: "rounded-small";
        md: "rounded-medium";
        lg: "rounded-large";
        full: "rounded-full";
    };
}, undefined, string[], tailwind_variants_dist_config.TVConfig<{
    color: {
        default: string;
        primary: string;
        secondary: string;
        success: string;
        warning: string;
        danger: string;
    };
    size: {
        sm: "text-small";
        md: "text-medium";
        lg: "text-large";
    };
    radius: {
        none: "rounded-none";
        sm: "rounded-small";
        md: "rounded-medium";
        lg: "rounded-large";
        full: "rounded-full";
    };
}, {
    color: {
        default: string;
        primary: string;
        secondary: string;
        success: string;
        warning: string;
        danger: string;
    };
    size: {
        sm: "text-small";
        md: "text-medium";
        lg: "text-large";
    };
    radius: {
        none: "rounded-none";
        sm: "rounded-small";
        md: "rounded-medium";
        lg: "rounded-large";
        full: "rounded-full";
    };
}>, unknown, unknown, undefined>>;
type CodeVariantProps = VariantProps<typeof code>;

export { CodeVariantProps, code };
