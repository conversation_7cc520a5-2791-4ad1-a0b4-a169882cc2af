import * as tailwind_variants from 'tailwind-variants';
import { VariantProps } from 'tailwind-variants';
import * as tailwind_variants_dist_config from 'tailwind-variants/dist/config';

/**
 * BreadcrumbsItem wrapper **Tailwind Variants** component
 *
 * const { base, item, separator } = breadcrumbItem({...})
 *
 * @example
 */
declare const breadcrumbItem: tailwind_variants.TVReturnType<{
    color: {
        foreground: {
            item: string;
            separator: string;
        };
        primary: {
            item: string;
            separator: string;
        };
        secondary: {
            item: string;
            separator: string;
        };
        success: {
            item: string;
            separator: string;
        };
        warning: {
            item: string;
            separator: string;
        };
        danger: {
            item: string;
            separator: string;
        };
    };
    size: {
        sm: {
            item: string;
        };
        md: {
            item: string;
        };
        lg: {
            item: string;
        };
    };
    underline: {
        none: {
            item: string;
        };
        hover: {
            item: string;
        };
        always: {
            item: string;
        };
        active: {
            item: string;
        };
        focus: {
            item: string;
        };
    };
    isCurrent: {
        true: {
            item: string;
        };
        false: {
            item: string[];
        };
    };
    isDisabled: {
        true: {
            item: string;
            separator: string;
        };
    };
    disableAnimation: {
        false: {
            item: string;
        };
        true: {
            item: string;
        };
    };
}, {
    base: string;
    item: string[];
    separator: string;
}, undefined, tailwind_variants_dist_config.TVConfig<{
    color: {
        foreground: {
            item: string;
            separator: string;
        };
        primary: {
            item: string;
            separator: string;
        };
        secondary: {
            item: string;
            separator: string;
        };
        success: {
            item: string;
            separator: string;
        };
        warning: {
            item: string;
            separator: string;
        };
        danger: {
            item: string;
            separator: string;
        };
    };
    size: {
        sm: {
            item: string;
        };
        md: {
            item: string;
        };
        lg: {
            item: string;
        };
    };
    underline: {
        none: {
            item: string;
        };
        hover: {
            item: string;
        };
        always: {
            item: string;
        };
        active: {
            item: string;
        };
        focus: {
            item: string;
        };
    };
    isCurrent: {
        true: {
            item: string;
        };
        false: {
            item: string[];
        };
    };
    isDisabled: {
        true: {
            item: string;
            separator: string;
        };
    };
    disableAnimation: {
        false: {
            item: string;
        };
        true: {
            item: string;
        };
    };
}, {
    color: {
        foreground: {
            item: string;
            separator: string;
        };
        primary: {
            item: string;
            separator: string;
        };
        secondary: {
            item: string;
            separator: string;
        };
        success: {
            item: string;
            separator: string;
        };
        warning: {
            item: string;
            separator: string;
        };
        danger: {
            item: string;
            separator: string;
        };
    };
    size: {
        sm: {
            item: string;
        };
        md: {
            item: string;
        };
        lg: {
            item: string;
        };
    };
    underline: {
        none: {
            item: string;
        };
        hover: {
            item: string;
        };
        always: {
            item: string;
        };
        active: {
            item: string;
        };
        focus: {
            item: string;
        };
    };
    isCurrent: {
        true: {
            item: string;
        };
        false: {
            item: string[];
        };
    };
    isDisabled: {
        true: {
            item: string;
            separator: string;
        };
    };
    disableAnimation: {
        false: {
            item: string;
        };
        true: {
            item: string;
        };
    };
}>, {
    color: {
        foreground: {
            item: string;
            separator: string;
        };
        primary: {
            item: string;
            separator: string;
        };
        secondary: {
            item: string;
            separator: string;
        };
        success: {
            item: string;
            separator: string;
        };
        warning: {
            item: string;
            separator: string;
        };
        danger: {
            item: string;
            separator: string;
        };
    };
    size: {
        sm: {
            item: string;
        };
        md: {
            item: string;
        };
        lg: {
            item: string;
        };
    };
    underline: {
        none: {
            item: string;
        };
        hover: {
            item: string;
        };
        always: {
            item: string;
        };
        active: {
            item: string;
        };
        focus: {
            item: string;
        };
    };
    isCurrent: {
        true: {
            item: string;
        };
        false: {
            item: string[];
        };
    };
    isDisabled: {
        true: {
            item: string;
            separator: string;
        };
    };
    disableAnimation: {
        false: {
            item: string;
        };
        true: {
            item: string;
        };
    };
}, {
    base: string;
    item: string[];
    separator: string;
}, tailwind_variants.TVReturnType<{
    color: {
        foreground: {
            item: string;
            separator: string;
        };
        primary: {
            item: string;
            separator: string;
        };
        secondary: {
            item: string;
            separator: string;
        };
        success: {
            item: string;
            separator: string;
        };
        warning: {
            item: string;
            separator: string;
        };
        danger: {
            item: string;
            separator: string;
        };
    };
    size: {
        sm: {
            item: string;
        };
        md: {
            item: string;
        };
        lg: {
            item: string;
        };
    };
    underline: {
        none: {
            item: string;
        };
        hover: {
            item: string;
        };
        always: {
            item: string;
        };
        active: {
            item: string;
        };
        focus: {
            item: string;
        };
    };
    isCurrent: {
        true: {
            item: string;
        };
        false: {
            item: string[];
        };
    };
    isDisabled: {
        true: {
            item: string;
            separator: string;
        };
    };
    disableAnimation: {
        false: {
            item: string;
        };
        true: {
            item: string;
        };
    };
}, {
    base: string;
    item: string[];
    separator: string;
}, undefined, tailwind_variants_dist_config.TVConfig<{
    color: {
        foreground: {
            item: string;
            separator: string;
        };
        primary: {
            item: string;
            separator: string;
        };
        secondary: {
            item: string;
            separator: string;
        };
        success: {
            item: string;
            separator: string;
        };
        warning: {
            item: string;
            separator: string;
        };
        danger: {
            item: string;
            separator: string;
        };
    };
    size: {
        sm: {
            item: string;
        };
        md: {
            item: string;
        };
        lg: {
            item: string;
        };
    };
    underline: {
        none: {
            item: string;
        };
        hover: {
            item: string;
        };
        always: {
            item: string;
        };
        active: {
            item: string;
        };
        focus: {
            item: string;
        };
    };
    isCurrent: {
        true: {
            item: string;
        };
        false: {
            item: string[];
        };
    };
    isDisabled: {
        true: {
            item: string;
            separator: string;
        };
    };
    disableAnimation: {
        false: {
            item: string;
        };
        true: {
            item: string;
        };
    };
}, {
    color: {
        foreground: {
            item: string;
            separator: string;
        };
        primary: {
            item: string;
            separator: string;
        };
        secondary: {
            item: string;
            separator: string;
        };
        success: {
            item: string;
            separator: string;
        };
        warning: {
            item: string;
            separator: string;
        };
        danger: {
            item: string;
            separator: string;
        };
    };
    size: {
        sm: {
            item: string;
        };
        md: {
            item: string;
        };
        lg: {
            item: string;
        };
    };
    underline: {
        none: {
            item: string;
        };
        hover: {
            item: string;
        };
        always: {
            item: string;
        };
        active: {
            item: string;
        };
        focus: {
            item: string;
        };
    };
    isCurrent: {
        true: {
            item: string;
        };
        false: {
            item: string[];
        };
    };
    isDisabled: {
        true: {
            item: string;
            separator: string;
        };
    };
    disableAnimation: {
        false: {
            item: string;
        };
        true: {
            item: string;
        };
    };
}>, unknown, unknown, undefined>>;
/**
 * Breadcrumbs wrapper **Tailwind Variants** component
 *
 * const { base, list, ellipsis, separator } = breadcrumbs({...})
 *
 * @example
 */
declare const breadcrumbs: tailwind_variants.TVReturnType<{
    size: {
        sm: {};
        md: {};
        lg: {};
    };
    radius: {
        none: {
            list: string;
        };
        sm: {
            list: string;
        };
        md: {
            list: string;
        };
        lg: {
            list: string;
        };
        full: {
            list: string;
        };
    };
    variant: {
        solid: {
            list: string;
        };
        bordered: {
            list: string;
        };
        light: {};
    };
}, {
    base: string;
    list: string;
    ellipsis: string;
    separator: string;
}, undefined, tailwind_variants_dist_config.TVConfig<{
    size: {
        sm: {};
        md: {};
        lg: {};
    };
    radius: {
        none: {
            list: string;
        };
        sm: {
            list: string;
        };
        md: {
            list: string;
        };
        lg: {
            list: string;
        };
        full: {
            list: string;
        };
    };
    variant: {
        solid: {
            list: string;
        };
        bordered: {
            list: string;
        };
        light: {};
    };
}, {
    size: {
        sm: {};
        md: {};
        lg: {};
    };
    radius: {
        none: {
            list: string;
        };
        sm: {
            list: string;
        };
        md: {
            list: string;
        };
        lg: {
            list: string;
        };
        full: {
            list: string;
        };
    };
    variant: {
        solid: {
            list: string;
        };
        bordered: {
            list: string;
        };
        light: {};
    };
}>, {
    size: {
        sm: {};
        md: {};
        lg: {};
    };
    radius: {
        none: {
            list: string;
        };
        sm: {
            list: string;
        };
        md: {
            list: string;
        };
        lg: {
            list: string;
        };
        full: {
            list: string;
        };
    };
    variant: {
        solid: {
            list: string;
        };
        bordered: {
            list: string;
        };
        light: {};
    };
}, {
    base: string;
    list: string;
    ellipsis: string;
    separator: string;
}, tailwind_variants.TVReturnType<{
    size: {
        sm: {};
        md: {};
        lg: {};
    };
    radius: {
        none: {
            list: string;
        };
        sm: {
            list: string;
        };
        md: {
            list: string;
        };
        lg: {
            list: string;
        };
        full: {
            list: string;
        };
    };
    variant: {
        solid: {
            list: string;
        };
        bordered: {
            list: string;
        };
        light: {};
    };
}, {
    base: string;
    list: string;
    ellipsis: string;
    separator: string;
}, undefined, tailwind_variants_dist_config.TVConfig<{
    size: {
        sm: {};
        md: {};
        lg: {};
    };
    radius: {
        none: {
            list: string;
        };
        sm: {
            list: string;
        };
        md: {
            list: string;
        };
        lg: {
            list: string;
        };
        full: {
            list: string;
        };
    };
    variant: {
        solid: {
            list: string;
        };
        bordered: {
            list: string;
        };
        light: {};
    };
}, {
    size: {
        sm: {};
        md: {};
        lg: {};
    };
    radius: {
        none: {
            list: string;
        };
        sm: {
            list: string;
        };
        md: {
            list: string;
        };
        lg: {
            list: string;
        };
        full: {
            list: string;
        };
    };
    variant: {
        solid: {
            list: string;
        };
        bordered: {
            list: string;
        };
        light: {};
    };
}>, unknown, unknown, undefined>>;
type BreadcrumbsVariantProps = VariantProps<typeof breadcrumbs>;
type BreadcrumbsSlots = keyof ReturnType<typeof breadcrumbs>;
type BreadcrumbItemVariantProps = VariantProps<typeof breadcrumbItem>;
type BreadcrumbItemSlots = keyof ReturnType<typeof breadcrumbItem>;

export { BreadcrumbItemSlots, BreadcrumbItemVariantProps, BreadcrumbsSlots, BreadcrumbsVariantProps, breadcrumbItem, breadcrumbs };
