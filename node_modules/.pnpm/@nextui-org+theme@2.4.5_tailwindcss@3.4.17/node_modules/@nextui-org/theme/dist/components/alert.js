"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/components/alert.ts
var alert_exports = {};
__export(alert_exports, {
  alert: () => alert
});
module.exports = __toCommonJS(alert_exports);

// src/utils/tv.ts
var import_tailwind_variants = require("tailwind-variants");

// src/utils/tw-merge-config.ts
var COMMON_UNITS = ["small", "medium", "large"];
var twMergeConfig = {
  theme: {
    opacity: ["disabled"],
    spacing: ["divider"],
    borderWidth: COMMON_UNITS,
    borderRadius: COMMON_UNITS
  },
  classGroups: {
    shadow: [{ shadow: COMMON_UNITS }],
    "font-size": [{ text: ["tiny", ...COMMON_UNITS] }],
    "bg-image": [
      "bg-stripe-gradient-default",
      "bg-stripe-gradient-primary",
      "bg-stripe-gradient-secondary",
      "bg-stripe-gradient-success",
      "bg-stripe-gradient-warning",
      "bg-stripe-gradient-danger"
    ]
  }
};

// src/utils/tv.ts
var tv = (options, config) => {
  var _a, _b, _c;
  return (0, import_tailwind_variants.tv)(options, {
    ...config,
    twMerge: (_a = config == null ? void 0 : config.twMerge) != null ? _a : true,
    twMergeConfig: {
      ...config == null ? void 0 : config.twMergeConfig,
      theme: {
        ...(_b = config == null ? void 0 : config.twMergeConfig) == null ? void 0 : _b.theme,
        ...twMergeConfig.theme
      },
      classGroups: {
        ...(_c = config == null ? void 0 : config.twMergeConfig) == null ? void 0 : _c.classGroups,
        ...twMergeConfig.classGroups
      }
    }
  });
};

// src/utils/variants.ts
var solid = {
  default: "bg-default text-default-foreground",
  primary: "bg-primary text-primary-foreground",
  secondary: "bg-secondary text-secondary-foreground",
  success: "bg-success text-success-foreground",
  warning: "bg-warning text-warning-foreground",
  danger: "bg-danger text-danger-foreground",
  foreground: "bg-foreground text-background"
};
var shadow = {
  default: "shadow-lg shadow-default/50 bg-default text-default-foreground",
  primary: "shadow-lg shadow-primary/40 bg-primary text-primary-foreground",
  secondary: "shadow-lg shadow-secondary/40 bg-secondary text-secondary-foreground",
  success: "shadow-lg shadow-success/40 bg-success text-success-foreground",
  warning: "shadow-lg shadow-warning/40 bg-warning text-warning-foreground",
  danger: "shadow-lg shadow-danger/40 bg-danger text-danger-foreground",
  foreground: "shadow-lg shadow-foreground/40 bg-foreground text-background"
};
var bordered = {
  default: "bg-transparent border-default text-foreground",
  primary: "bg-transparent border-primary text-primary",
  secondary: "bg-transparent border-secondary text-secondary",
  success: "bg-transparent border-success text-success",
  warning: "bg-transparent border-warning text-warning",
  danger: "bg-transparent border-danger text-danger",
  foreground: "bg-transparent border-foreground text-foreground"
};
var flat = {
  default: "bg-default/40 text-default-700",
  primary: "bg-primary/20 text-primary-600",
  secondary: "bg-secondary/20 text-secondary-600",
  success: "bg-success/20 text-success-700 dark:text-success",
  warning: "bg-warning/20 text-warning-700 dark:text-warning",
  danger: "bg-danger/20 text-danger-600 dark:text-danger-500",
  foreground: "bg-foreground/10 text-foreground"
};
var faded = {
  default: "border-default bg-default-100 text-default-foreground",
  primary: "border-default bg-default-100 text-primary",
  secondary: "border-default bg-default-100 text-secondary",
  success: "border-default bg-default-100 text-success",
  warning: "border-default bg-default-100 text-warning",
  danger: "border-default bg-default-100 text-danger",
  foreground: "border-default bg-default-100 text-foreground"
};
var light = {
  default: "bg-transparent text-default-foreground",
  primary: "bg-transparent text-primary",
  secondary: "bg-transparent text-secondary",
  success: "bg-transparent text-success",
  warning: "bg-transparent text-warning",
  danger: "bg-transparent text-danger",
  foreground: "bg-transparent text-foreground"
};
var ghost = {
  default: "border-default text-default-foreground",
  primary: "border-primary text-primary",
  secondary: "border-secondary text-secondary",
  success: "border-success text-success",
  warning: "border-warning text-warning",
  danger: "border-danger text-danger",
  foreground: "border-foreground text-foreground hover:!bg-foreground"
};
var colorVariants = {
  solid,
  shadow,
  bordered,
  flat,
  faded,
  light,
  ghost
};

// src/components/alert.ts
var alert = tv({
  slots: {
    base: "flex flex-grow flex-row w-full items-start py-3 px-4 gap-x-1",
    mainWrapper: "h-full flex-grow min-h-10 ms-2 flex flex-col box-border items-start text-inherit justify-center",
    title: "text-small w-full font-medium block text-inherit leading-5",
    description: "pl-[1px] text-small font-normal text-inherit",
    closeButton: "relative text-inherit translate-x-1 -translate-y-1",
    iconWrapper: "flex-none relative w-9 h-9 rounded-full grid place-items-center",
    alertIcon: "fill-current w-6 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
  },
  variants: {
    color: {
      default: {},
      primary: {},
      secondary: {},
      success: {},
      warning: {},
      danger: {}
    },
    variant: {
      solid: {},
      flat: {},
      faded: {
        base: "border-small"
      },
      bordered: {
        base: "border-small bg-transparent"
      }
    },
    radius: {
      none: {
        base: "rounded-none"
      },
      sm: {
        base: "rounded-small"
      },
      md: {
        base: "rounded-medium"
      },
      lg: {
        base: "rounded-large"
      },
      full: {
        base: "rounded-full"
      }
    },
    hideIcon: {
      true: {
        iconWrapper: "hidden"
      }
    },
    hideIconWrapper: {
      true: {
        base: "gap-x-0",
        iconWrapper: "!bg-transparent !shadow-none !border-none"
      }
    },
    hasContent: {
      false: {
        base: "items-start",
        mainWrapper: "justify-center items-center"
      }
    }
  },
  defaultVariants: {
    color: "default",
    variant: "flat",
    radius: "md",
    hideIcon: false,
    hideIconWrapper: false
  },
  compoundVariants: [
    {
      variant: "solid",
      color: "default",
      class: {
        base: colorVariants.solid.default,
        closeButton: "data-[hover]:bg-default-100",
        alertIcon: "text-default-foreground"
      }
    },
    {
      variant: "solid",
      color: "primary",
      class: {
        base: colorVariants.solid.primary
      }
    },
    {
      variant: "solid",
      color: "secondary",
      class: {
        base: colorVariants.solid.secondary
      }
    },
    {
      variant: "solid",
      color: "success",
      class: {
        base: colorVariants.solid.success
      }
    },
    {
      variant: "solid",
      color: "warning",
      class: {
        base: colorVariants.solid.warning
      }
    },
    {
      variant: "solid",
      color: "danger",
      class: {
        base: colorVariants.solid.danger
      }
    },
    {
      variant: ["flat", "faded"],
      color: "default",
      class: {
        base: [
          colorVariants.flat.default,
          "bg-default-100 dark:bg-default-50/50",
          "text-default-foreground"
        ],
        description: "text-default-600",
        closeButton: "text-default-400",
        iconWrapper: "bg-default-50 dark:bg-default-100 border-default-200"
      }
    },
    {
      variant: ["flat", "faded"],
      color: "primary",
      class: {
        base: [colorVariants.flat.primary, "bg-primary-50 dark:bg-primary-50/50"],
        closeButton: "text-primary-500 data-[hover]:bg-primary-200",
        iconWrapper: "bg-primary-50 dark:bg-primary-100 border-primary-100"
      }
    },
    {
      variant: ["flat", "faded"],
      color: "secondary",
      class: {
        base: [colorVariants.flat.secondary, "bg-secondary-50 dark:bg-secondary-50/50"],
        closeButton: "text-secondary-500 data-[hover]:bg-secondary-200",
        iconWrapper: "bg-secondary-50 dark:bg-secondary-100 border-secondary-100"
      }
    },
    {
      variant: ["flat", "faded"],
      color: "success",
      class: {
        base: [colorVariants.flat.success, "bg-success-50 dark:bg-success-50/50"],
        closeButton: "text-success-500 data-[hover]:bg-success-200",
        iconWrapper: "bg-success-50 dark:bg-success-100 border-success-100"
      }
    },
    {
      variant: ["flat", "faded"],
      color: "warning",
      class: {
        base: [colorVariants.flat.warning, "bg-warning-50 dark:bg-warning-50/50"],
        closeButton: "text-warning-500 data-[hover]:bg-warning-200",
        iconWrapper: "bg-warning-50 dark:bg-warning-100 border-warning-100"
      }
    },
    {
      variant: ["flat", "faded"],
      color: "danger",
      class: {
        base: [colorVariants.flat.danger, "bg-danger-50 dark:bg-danger-50/50"],
        closeButton: "text-danger-500 data-[hover]:bg-danger-200",
        iconWrapper: "bg-danger-50 dark:bg-danger-100 border-danger-100"
      }
    },
    {
      variant: "faded",
      color: "default",
      class: {
        base: "border-default-300 dark:border-default-200"
      }
    },
    {
      variant: "faded",
      color: "primary",
      class: {
        base: "border-primary-200 dark:border-primary-100"
      }
    },
    {
      variant: "faded",
      color: "secondary",
      class: {
        base: "border-secondary-200"
      }
    },
    {
      variant: "faded",
      color: "success",
      class: {
        base: "border-success-300 dark:border-success-100"
      }
    },
    {
      variant: "faded",
      color: "warning",
      class: {
        base: "border-warning-300 dark:border-warning-100"
      }
    },
    {
      variant: "faded",
      color: "danger",
      class: {
        base: "border-danger-200 dark:border-danger-100"
      }
    },
    {
      variant: "bordered",
      color: "default",
      class: {
        base: [colorVariants.bordered.default],
        description: "text-default-600",
        closeButton: "text-default-400"
      }
    },
    {
      variant: "bordered",
      color: "primary",
      class: {
        base: [colorVariants.bordered.primary],
        closeButton: "data-[hover]:bg-primary-50"
      }
    },
    {
      variant: "bordered",
      color: "secondary",
      class: {
        base: [colorVariants.bordered.secondary],
        closeButton: "data-[hover]:bg-secondary-50"
      }
    },
    {
      variant: "bordered",
      color: "success",
      class: {
        base: [colorVariants.bordered.success],
        closeButton: "data-[hover]:bg-success-50"
      }
    },
    {
      variant: "bordered",
      color: "warning",
      class: {
        base: [colorVariants.bordered.warning],
        closeButton: "data-[hover]:bg-warning-100"
      }
    },
    {
      variant: "bordered",
      color: "danger",
      class: {
        base: [colorVariants.bordered.danger],
        closeButton: "data-[hover]:bg-danger-50"
      }
    },
    {
      variant: ["flat", "bordered", "faded"],
      class: {
        iconWrapper: "shadow-small"
      }
    },
    {
      variant: ["flat", "faded"],
      class: {
        iconWrapper: "shadow-small border-1"
      }
    },
    {
      variant: "bordered",
      color: "default",
      class: {
        iconWrapper: "bg-default-200 dark:bg-default-100"
      }
    },
    {
      variant: "bordered",
      color: "primary",
      class: {
        iconWrapper: "bg-primary-100 dark:bg-primary-50"
      }
    },
    {
      variant: "bordered",
      color: "secondary",
      class: {
        iconWrapper: "bg-secondary-100 dark:bg-secondary-50"
      }
    },
    {
      variant: "bordered",
      color: "success",
      class: {
        iconWrapper: "bg-success-100 dark:bg-success-50"
      }
    },
    {
      variant: "bordered",
      color: "warning",
      class: {
        iconWrapper: "bg-warning-100 dark:bg-warning-50"
      }
    },
    {
      variant: "bordered",
      color: "danger",
      class: {
        iconWrapper: "bg-danger-100 dark:bg-danger-50"
      }
    }
  ]
});
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  alert
});
