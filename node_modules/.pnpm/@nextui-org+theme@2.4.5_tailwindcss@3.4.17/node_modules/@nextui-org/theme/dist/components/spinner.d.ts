import * as tailwind_variants from 'tailwind-variants';
import { VariantProps } from 'tailwind-variants';
import * as tailwind_variants_dist_config from 'tailwind-variants/dist/config';

/**
 * Spinner wrapper **Tailwind Variants** component
 *
 * const {base, circle1, circle2, label } = spinner({...})
 *
 * @example
 * <div className={base())}>
 *    <i className={circle1()}/>
 *    <i className={circle2()}/>
 *    <span className={label()}/>
 * </div>
 */
declare const spinner: tailwind_variants.TVReturnType<{
    size: {
        sm: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
        md: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
        lg: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
    };
    color: {
        current: {
            circle1: string;
            circle2: string;
        };
        white: {
            circle1: string;
            circle2: string;
        };
        default: {
            circle1: string;
            circle2: string;
        };
        primary: {
            circle1: string;
            circle2: string;
        };
        secondary: {
            circle1: string;
            circle2: string;
        };
        success: {
            circle1: string;
            circle2: string;
        };
        warning: {
            circle1: string;
            circle2: string;
        };
        danger: {
            circle1: string;
            circle2: string;
        };
    };
    labelColor: {
        foreground: {
            label: string;
        };
        primary: {
            label: string;
        };
        secondary: {
            label: string;
        };
        success: {
            label: string;
        };
        warning: {
            label: string;
        };
        danger: {
            label: string;
        };
    };
}, {
    base: string;
    wrapper: string;
    circle1: string[];
    circle2: string[];
    label: string;
}, undefined, tailwind_variants_dist_config.TVConfig<{
    size: {
        sm: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
        md: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
        lg: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
    };
    color: {
        current: {
            circle1: string;
            circle2: string;
        };
        white: {
            circle1: string;
            circle2: string;
        };
        default: {
            circle1: string;
            circle2: string;
        };
        primary: {
            circle1: string;
            circle2: string;
        };
        secondary: {
            circle1: string;
            circle2: string;
        };
        success: {
            circle1: string;
            circle2: string;
        };
        warning: {
            circle1: string;
            circle2: string;
        };
        danger: {
            circle1: string;
            circle2: string;
        };
    };
    labelColor: {
        foreground: {
            label: string;
        };
        primary: {
            label: string;
        };
        secondary: {
            label: string;
        };
        success: {
            label: string;
        };
        warning: {
            label: string;
        };
        danger: {
            label: string;
        };
    };
}, {
    size: {
        sm: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
        md: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
        lg: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
    };
    color: {
        current: {
            circle1: string;
            circle2: string;
        };
        white: {
            circle1: string;
            circle2: string;
        };
        default: {
            circle1: string;
            circle2: string;
        };
        primary: {
            circle1: string;
            circle2: string;
        };
        secondary: {
            circle1: string;
            circle2: string;
        };
        success: {
            circle1: string;
            circle2: string;
        };
        warning: {
            circle1: string;
            circle2: string;
        };
        danger: {
            circle1: string;
            circle2: string;
        };
    };
    labelColor: {
        foreground: {
            label: string;
        };
        primary: {
            label: string;
        };
        secondary: {
            label: string;
        };
        success: {
            label: string;
        };
        warning: {
            label: string;
        };
        danger: {
            label: string;
        };
    };
}>, {
    size: {
        sm: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
        md: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
        lg: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
    };
    color: {
        current: {
            circle1: string;
            circle2: string;
        };
        white: {
            circle1: string;
            circle2: string;
        };
        default: {
            circle1: string;
            circle2: string;
        };
        primary: {
            circle1: string;
            circle2: string;
        };
        secondary: {
            circle1: string;
            circle2: string;
        };
        success: {
            circle1: string;
            circle2: string;
        };
        warning: {
            circle1: string;
            circle2: string;
        };
        danger: {
            circle1: string;
            circle2: string;
        };
    };
    labelColor: {
        foreground: {
            label: string;
        };
        primary: {
            label: string;
        };
        secondary: {
            label: string;
        };
        success: {
            label: string;
        };
        warning: {
            label: string;
        };
        danger: {
            label: string;
        };
    };
}, {
    base: string;
    wrapper: string;
    circle1: string[];
    circle2: string[];
    label: string;
}, tailwind_variants.TVReturnType<{
    size: {
        sm: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
        md: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
        lg: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
    };
    color: {
        current: {
            circle1: string;
            circle2: string;
        };
        white: {
            circle1: string;
            circle2: string;
        };
        default: {
            circle1: string;
            circle2: string;
        };
        primary: {
            circle1: string;
            circle2: string;
        };
        secondary: {
            circle1: string;
            circle2: string;
        };
        success: {
            circle1: string;
            circle2: string;
        };
        warning: {
            circle1: string;
            circle2: string;
        };
        danger: {
            circle1: string;
            circle2: string;
        };
    };
    labelColor: {
        foreground: {
            label: string;
        };
        primary: {
            label: string;
        };
        secondary: {
            label: string;
        };
        success: {
            label: string;
        };
        warning: {
            label: string;
        };
        danger: {
            label: string;
        };
    };
}, {
    base: string;
    wrapper: string;
    circle1: string[];
    circle2: string[];
    label: string;
}, undefined, tailwind_variants_dist_config.TVConfig<{
    size: {
        sm: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
        md: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
        lg: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
    };
    color: {
        current: {
            circle1: string;
            circle2: string;
        };
        white: {
            circle1: string;
            circle2: string;
        };
        default: {
            circle1: string;
            circle2: string;
        };
        primary: {
            circle1: string;
            circle2: string;
        };
        secondary: {
            circle1: string;
            circle2: string;
        };
        success: {
            circle1: string;
            circle2: string;
        };
        warning: {
            circle1: string;
            circle2: string;
        };
        danger: {
            circle1: string;
            circle2: string;
        };
    };
    labelColor: {
        foreground: {
            label: string;
        };
        primary: {
            label: string;
        };
        secondary: {
            label: string;
        };
        success: {
            label: string;
        };
        warning: {
            label: string;
        };
        danger: {
            label: string;
        };
    };
}, {
    size: {
        sm: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
        md: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
        lg: {
            wrapper: string;
            circle1: string;
            circle2: string;
            label: string;
        };
    };
    color: {
        current: {
            circle1: string;
            circle2: string;
        };
        white: {
            circle1: string;
            circle2: string;
        };
        default: {
            circle1: string;
            circle2: string;
        };
        primary: {
            circle1: string;
            circle2: string;
        };
        secondary: {
            circle1: string;
            circle2: string;
        };
        success: {
            circle1: string;
            circle2: string;
        };
        warning: {
            circle1: string;
            circle2: string;
        };
        danger: {
            circle1: string;
            circle2: string;
        };
    };
    labelColor: {
        foreground: {
            label: string;
        };
        primary: {
            label: string;
        };
        secondary: {
            label: string;
        };
        success: {
            label: string;
        };
        warning: {
            label: string;
        };
        danger: {
            label: string;
        };
    };
}>, unknown, unknown, undefined>>;
type SpinnerVariantProps = VariantProps<typeof spinner>;
type SpinnerSlots = keyof ReturnType<typeof spinner>;

export { SpinnerSlots, SpinnerVariantProps, spinner };
