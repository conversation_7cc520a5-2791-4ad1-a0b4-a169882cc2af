import * as tailwind_variants_dist_config from 'tailwind-variants/dist/config';
import * as tailwind_variants from 'tailwind-variants';
import { VariantProps } from 'tailwind-variants';

/**
 * DatePicker wrapper **Tailwind Variants** component
 *
 * @example
 */
declare const datePicker: tailwind_variants.TVReturnType<{
    [key: string]: {
        [key: string]: tailwind_variants.ClassValue | {
            base?: tailwind_variants.ClassValue;
            selectorIcon?: tailwind_variants.ClassValue;
            popoverContent?: tailwind_variants.ClassValue;
            selectorButton?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            calendarContent?: tailwind_variants.ClassValue;
            timeInputLabel?: tailwind_variants.ClassValue;
            timeInput?: tailwind_variants.ClassValue;
        };
    };
} | {
    [x: string]: {
        [x: string]: tailwind_variants.ClassValue | {
            base?: tailwind_variants.ClassValue;
            selectorIcon?: tailwind_variants.ClassValue;
            popoverContent?: tailwind_variants.ClassValue;
            selectorButton?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            calendarContent?: tailwind_variants.ClassValue;
            timeInputLabel?: tailwind_variants.ClassValue;
            timeInput?: tailwind_variants.ClassValue;
        };
    };
} | {}, {
    base: string;
    selectorButton: string;
    selectorIcon: string;
    popoverContent: string;
    calendar: string;
    calendarContent: string;
    timeInputLabel: string;
    timeInput: string;
}, undefined, tailwind_variants_dist_config.TVConfig<unknown, {
    [key: string]: {
        [key: string]: tailwind_variants.ClassValue | {
            base?: tailwind_variants.ClassValue;
            selectorIcon?: tailwind_variants.ClassValue;
            popoverContent?: tailwind_variants.ClassValue;
            selectorButton?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            calendarContent?: tailwind_variants.ClassValue;
            timeInputLabel?: tailwind_variants.ClassValue;
            timeInput?: tailwind_variants.ClassValue;
        };
    };
} | {}>, {
    [key: string]: {
        [key: string]: tailwind_variants.ClassValue | {
            base?: tailwind_variants.ClassValue;
            selectorIcon?: tailwind_variants.ClassValue;
            popoverContent?: tailwind_variants.ClassValue;
            selectorButton?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            calendarContent?: tailwind_variants.ClassValue;
            timeInputLabel?: tailwind_variants.ClassValue;
            timeInput?: tailwind_variants.ClassValue;
        };
    };
} | {}, {
    base: string;
    selectorButton: string;
    selectorIcon: string;
    popoverContent: string;
    calendar: string;
    calendarContent: string;
    timeInputLabel: string;
    timeInput: string;
}, tailwind_variants.TVReturnType<unknown, {
    base: string;
    selectorButton: string;
    selectorIcon: string;
    popoverContent: string;
    calendar: string;
    calendarContent: string;
    timeInputLabel: string;
    timeInput: string;
}, undefined, tailwind_variants_dist_config.TVConfig<unknown, {
    [key: string]: {
        [key: string]: tailwind_variants.ClassValue | {
            base?: tailwind_variants.ClassValue;
            selectorIcon?: tailwind_variants.ClassValue;
            popoverContent?: tailwind_variants.ClassValue;
            selectorButton?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            calendarContent?: tailwind_variants.ClassValue;
            timeInputLabel?: tailwind_variants.ClassValue;
            timeInput?: tailwind_variants.ClassValue;
        };
    };
} | {}>, unknown, unknown, undefined>>;
declare const dateRangePicker: tailwind_variants.TVReturnType<{
    [key: string]: {
        [key: string]: tailwind_variants.ClassValue | {
            separator?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            bottomContent?: tailwind_variants.ClassValue;
            timeInputWrapper?: tailwind_variants.ClassValue;
        };
    };
} | {
    [x: string]: {
        [x: string]: tailwind_variants.ClassValue | {
            separator?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            bottomContent?: tailwind_variants.ClassValue;
            timeInputWrapper?: tailwind_variants.ClassValue;
        };
    };
} | {
    [x: string]: {
        [x: string]: tailwind_variants.ClassValue | {
            separator?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            bottomContent?: tailwind_variants.ClassValue;
            timeInputWrapper?: tailwind_variants.ClassValue;
        };
    };
} | {}, {
    calendar: string;
    bottomContent: string;
    timeInputWrapper: string;
    separator: string;
}, undefined, tailwind_variants_dist_config.TVConfig<{
    [key: string]: {
        [key: string]: tailwind_variants.ClassValue | {
            separator?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            bottomContent?: tailwind_variants.ClassValue;
            timeInputWrapper?: tailwind_variants.ClassValue;
        };
    };
} | {
    [x: string]: {
        [x: string]: tailwind_variants.ClassValue | {
            separator?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            bottomContent?: tailwind_variants.ClassValue;
            timeInputWrapper?: tailwind_variants.ClassValue;
        };
    };
} | {
    [x: string]: {
        [x: string]: tailwind_variants.ClassValue | {
            separator?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            bottomContent?: tailwind_variants.ClassValue;
            timeInputWrapper?: tailwind_variants.ClassValue;
        };
    };
} | {}, {
    [key: string]: {
        [key: string]: tailwind_variants.ClassValue | {
            base?: tailwind_variants.ClassValue;
            selectorIcon?: tailwind_variants.ClassValue;
            popoverContent?: tailwind_variants.ClassValue;
            selectorButton?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            calendarContent?: tailwind_variants.ClassValue;
            timeInputLabel?: tailwind_variants.ClassValue;
            timeInput?: tailwind_variants.ClassValue;
        };
    };
} | {
    [x: string]: {
        [x: string]: tailwind_variants.ClassValue | {
            base?: tailwind_variants.ClassValue;
            selectorIcon?: tailwind_variants.ClassValue;
            popoverContent?: tailwind_variants.ClassValue;
            selectorButton?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            calendarContent?: tailwind_variants.ClassValue;
            timeInputLabel?: tailwind_variants.ClassValue;
            timeInput?: tailwind_variants.ClassValue;
        };
    };
} | {}>, {
    [key: string]: {
        [key: string]: tailwind_variants.ClassValue | {
            base?: tailwind_variants.ClassValue;
            selectorIcon?: tailwind_variants.ClassValue;
            popoverContent?: tailwind_variants.ClassValue;
            selectorButton?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            calendarContent?: tailwind_variants.ClassValue;
            timeInputLabel?: tailwind_variants.ClassValue;
            timeInput?: tailwind_variants.ClassValue;
        };
    };
} | {
    [x: string]: {
        [x: string]: tailwind_variants.ClassValue | {
            base?: tailwind_variants.ClassValue;
            selectorIcon?: tailwind_variants.ClassValue;
            popoverContent?: tailwind_variants.ClassValue;
            selectorButton?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            calendarContent?: tailwind_variants.ClassValue;
            timeInputLabel?: tailwind_variants.ClassValue;
            timeInput?: tailwind_variants.ClassValue;
        };
    };
} | {}, {
    base: string;
    selectorButton: string;
    selectorIcon: string;
    popoverContent: string;
    calendar: string;
    calendarContent: string;
    timeInputLabel: string;
    timeInput: string;
}, tailwind_variants.TVReturnType<{
    [key: string]: {
        [key: string]: tailwind_variants.ClassValue | {
            base?: tailwind_variants.ClassValue;
            selectorIcon?: tailwind_variants.ClassValue;
            popoverContent?: tailwind_variants.ClassValue;
            selectorButton?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            calendarContent?: tailwind_variants.ClassValue;
            timeInputLabel?: tailwind_variants.ClassValue;
            timeInput?: tailwind_variants.ClassValue;
        };
    };
} | {
    [x: string]: {
        [x: string]: tailwind_variants.ClassValue | {
            base?: tailwind_variants.ClassValue;
            selectorIcon?: tailwind_variants.ClassValue;
            popoverContent?: tailwind_variants.ClassValue;
            selectorButton?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            calendarContent?: tailwind_variants.ClassValue;
            timeInputLabel?: tailwind_variants.ClassValue;
            timeInput?: tailwind_variants.ClassValue;
        };
    };
} | {}, {
    base: string;
    selectorButton: string;
    selectorIcon: string;
    popoverContent: string;
    calendar: string;
    calendarContent: string;
    timeInputLabel: string;
    timeInput: string;
}, undefined, tailwind_variants_dist_config.TVConfig<unknown, {
    [key: string]: {
        [key: string]: tailwind_variants.ClassValue | {
            base?: tailwind_variants.ClassValue;
            selectorIcon?: tailwind_variants.ClassValue;
            popoverContent?: tailwind_variants.ClassValue;
            selectorButton?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            calendarContent?: tailwind_variants.ClassValue;
            timeInputLabel?: tailwind_variants.ClassValue;
            timeInput?: tailwind_variants.ClassValue;
        };
    };
} | {}>, {
    [key: string]: {
        [key: string]: tailwind_variants.ClassValue | {
            base?: tailwind_variants.ClassValue;
            selectorIcon?: tailwind_variants.ClassValue;
            popoverContent?: tailwind_variants.ClassValue;
            selectorButton?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            calendarContent?: tailwind_variants.ClassValue;
            timeInputLabel?: tailwind_variants.ClassValue;
            timeInput?: tailwind_variants.ClassValue;
        };
    };
} | {}, {
    base: string;
    selectorButton: string;
    selectorIcon: string;
    popoverContent: string;
    calendar: string;
    calendarContent: string;
    timeInputLabel: string;
    timeInput: string;
}, tailwind_variants.TVReturnType<unknown, {
    base: string;
    selectorButton: string;
    selectorIcon: string;
    popoverContent: string;
    calendar: string;
    calendarContent: string;
    timeInputLabel: string;
    timeInput: string;
}, undefined, tailwind_variants_dist_config.TVConfig<unknown, {
    [key: string]: {
        [key: string]: tailwind_variants.ClassValue | {
            base?: tailwind_variants.ClassValue;
            selectorIcon?: tailwind_variants.ClassValue;
            popoverContent?: tailwind_variants.ClassValue;
            selectorButton?: tailwind_variants.ClassValue;
            calendar?: tailwind_variants.ClassValue;
            calendarContent?: tailwind_variants.ClassValue;
            timeInputLabel?: tailwind_variants.ClassValue;
            timeInput?: tailwind_variants.ClassValue;
        };
    };
} | {}>, unknown, unknown, undefined>>>;
/** Base */
type DatePickerReturnType = ReturnType<typeof datePicker>;
type DatePickerVariantProps = VariantProps<typeof datePicker>;
type DatePickerSlots = keyof ReturnType<typeof datePicker>;
/** Range */
type DateRangePickerReturnType = ReturnType<typeof dateRangePicker>;
type DateRangePickerVariantProps = VariantProps<typeof dateRangePicker>;
type DateRangePickerSlots = keyof ReturnType<typeof dateRangePicker>;

export { DatePickerReturnType, DatePickerSlots, DatePickerVariantProps, DateRangePickerReturnType, DateRangePickerSlots, DateRangePickerVariantProps, datePicker, dateRangePicker };
