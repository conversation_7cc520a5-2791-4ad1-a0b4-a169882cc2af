import * as tailwind_variants from 'tailwind-variants';
import * as tailwind_variants_dist_config from 'tailwind-variants/dist/config';

/**
 * Drip wrapper **Tailwind Variants** component
 *
 * const classNames = drip({...})
 *
 * @example
 * <span ref={dripRef} className={classNames())} />
 */
declare const drip: tailwind_variants.TVReturnType<{} | {} | {}, undefined, string[], tailwind_variants_dist_config.TVConfig<unknown, {} | {}>, {} | {}, undefined, tailwind_variants.TVReturnType<unknown, undefined, string[], tailwind_variants_dist_config.TVConfig<unknown, {} | {}>, unknown, unknown, undefined>>;

export { drip };
