import {
  tv
} from "./chunk-UWE6H66T.mjs";
import {
  dataFocusVisibleClasses
} from "./chunk-GH5E4FQB.mjs";

// src/components/menu.ts
var menu = tv({
  slots: {
    base: "w-full relative flex flex-col gap-1 p-1 overflow-clip",
    list: "w-full flex flex-col gap-0.5 outline-none",
    emptyContent: [
      "h-10",
      "px-2",
      "py-1.5",
      "w-full",
      "h-full",
      "text-foreground-400",
      "text-start"
    ]
  }
});
var menuItem = tv({
  slots: {
    base: [
      "flex",
      "group",
      "gap-2",
      "items-center",
      "justify-between",
      "relative",
      "px-2",
      "py-1.5",
      "w-full",
      "h-full",
      "box-border",
      "rounded-small",
      "subpixel-antialiased",
      "outline-none",
      "cursor-pointer",
      "tap-highlight-transparent",
      ...dataFocusVisibleClasses,
      "data-[focus-visible=true]:dark:ring-offset-background-content1"
    ],
    wrapper: "w-full flex flex-col items-start justify-center",
    title: "flex-1 text-small font-normal",
    description: ["w-full", "text-tiny", "text-foreground-500", "group-hover:text-current"],
    selectedIcon: ["text-inherit", "w-3", "h-3", "flex-shrink-0"],
    shortcut: [
      "px-1",
      "py-0.5",
      "rounded",
      "font-sans",
      "text-foreground-500",
      "text-tiny",
      "border-small",
      "border-default-300",
      "group-hover:border-current"
    ]
  },
  variants: {
    variant: {
      solid: {
        base: ""
      },
      bordered: {
        base: "border-medium border-transparent bg-transparent"
      },
      light: {
        base: "bg-transparent"
      },
      faded: {
        base: [
          "border-small border-transparent hover:border-default data-[hover=true]:bg-default-100",
          "data-[selectable=true]:focus:border-default data-[selectable=true]:focus:bg-default-100"
        ]
      },
      flat: {
        base: ""
      },
      shadow: {
        base: "data-[hover=true]:shadow-lg"
      }
    },
    color: {
      default: {},
      primary: {},
      secondary: {},
      success: {},
      warning: {},
      danger: {}
    },
    showDivider: {
      true: {
        base: [
          "mb-1.5",
          "after:content-['']",
          "after:absolute",
          "after:-bottom-1",
          "after:left-0",
          "after:right-0",
          "after:h-divider",
          "after:bg-divider"
        ]
      },
      false: {}
    },
    isDisabled: {
      true: {
        base: "opacity-disabled pointer-events-none"
      }
    },
    disableAnimation: {
      true: {},
      false: {
        base: "data-[hover=true]:transition-colors"
      }
    },
    hasTitleTextChild: {
      true: {
        title: "truncate"
      }
    },
    hasDescriptionTextChild: {
      true: {
        description: "truncate"
      }
    }
  },
  defaultVariants: {
    variant: "solid",
    color: "default",
    showDivider: false
  },
  compoundVariants: [
    {
      variant: "solid",
      color: "default",
      class: {
        base: [
          "data-[hover=true]:bg-default",
          "data-[hover=true]:text-default-foreground",
          "data-[selectable=true]:focus:bg-default",
          "data-[selectable=true]:focus:text-default-foreground"
        ]
      }
    },
    {
      variant: "solid",
      color: "primary",
      class: {
        base: [
          "data-[hover=true]:bg-primary data-[hover=true]:text-primary-foreground",
          "data-[selectable=true]:focus:bg-primary data-[selectable=true]:focus:text-primary-foreground"
        ]
      }
    },
    {
      variant: "solid",
      color: "secondary",
      class: {
        base: [
          "data-[hover=true]:bg-secondary data-[hover=true]:text-secondary-foreground",
          "data-[selectable=true]:focus:bg-secondary data-[selectable=true]:focus:text-secondary-foreground"
        ]
      }
    },
    {
      variant: "solid",
      color: "success",
      class: {
        base: [
          "data-[hover=true]:bg-success data-[hover=true]:text-success-foreground",
          "data-[selectable=true]:focus:bg-success data-[selectable=true]:focus:text-success-foreground"
        ]
      }
    },
    {
      variant: "solid",
      color: "warning",
      class: {
        base: [
          "data-[hover=true]:bg-warning data-[hover=true]:text-warning-foreground",
          "data-[selectable=true]:focus:bg-warning data-[selectable=true]:focus:text-warning-foreground"
        ]
      }
    },
    {
      variant: "solid",
      color: "danger",
      class: {
        base: [
          "data-[hover=true]:bg-danger data-[hover=true]:text-danger-foreground",
          "data-[selectable=true]:focus:bg-danger data-[selectable=true]:focus:text-danger-foreground"
        ]
      }
    },
    {
      variant: "shadow",
      color: "default",
      class: {
        base: [
          "data-[hover=true]:shadow-default/50 data-[hover=true]:bg-default data-[hover=true]:text-default-foreground",
          "data-[selectable=true]:focus:shadow-default/50 data-[selectable=true]:focus:bg-default data-[selectable=true]:focus:text-default-foreground"
        ]
      }
    },
    {
      variant: "shadow",
      color: "primary",
      class: {
        base: [
          "data-[hover=true]:shadow-primary/30 data-[hover=true]:bg-primary data-[hover=true]:text-primary-foreground",
          "data-[selectable=true]:focus:shadow-primary/30 data-[selectable=true]:focus:bg-primary data-[selectable=true]:focus:text-primary-foreground"
        ]
      }
    },
    {
      variant: "shadow",
      color: "secondary",
      class: {
        base: [
          "data-[hover=true]:shadow-secondary/30 data-[hover=true]:bg-secondary data-[hover=true]:text-secondary-foreground",
          "data-[selectable=true]:focus:shadow-secondary/30 data-[selectable=true]:focus:bg-secondary data-[selectable=true]:focus:text-secondary-foreground"
        ]
      }
    },
    {
      variant: "shadow",
      color: "success",
      class: {
        base: [
          "data-[hover=true]:shadow-success/30 data-[hover=true]:bg-success data-[hover=true]:text-success-foreground",
          "data-[selectable=true]:focus:shadow-success/30 data-[selectable=true]:focus:bg-success data-[selectable=true]:focus:text-success-foreground"
        ]
      }
    },
    {
      variant: "shadow",
      color: "warning",
      class: {
        base: [
          "data-[hover=true]:shadow-warning/30 data-[hover=true]:bg-warning data-[hover=true]:text-warning-foreground",
          "data-[selectable=true]:focus:shadow-warning/30 data-[selectable=true]:focus:bg-warning data-[selectable=true]:focus:text-warning-foreground"
        ]
      }
    },
    {
      variant: "shadow",
      color: "danger",
      class: {
        base: [
          "data-[hover=true]:shadow-danger/30 data-[hover=true]:bg-danger data-[hover=true]:text-danger-foreground",
          "data-[selectable=true]:focus:shadow-danger/30 data-[selectable=true]:focus:bg-danger data-[selectable=true]:focus:text-danger-foreground"
        ]
      }
    },
    {
      variant: "bordered",
      color: "default",
      class: {
        base: ["data-[hover=true]:border-default", "data-[selectable=true]:focus:border-default"]
      }
    },
    {
      variant: "bordered",
      color: "primary",
      class: {
        base: [
          "data-[hover=true]:border-primary data-[hover=true]:text-primary",
          "data-[selectable=true]:focus:border-primary data-[selectable=true]:focus:text-primary"
        ]
      }
    },
    {
      variant: "bordered",
      color: "secondary",
      class: {
        base: [
          "data-[hover=true]:border-secondary data-[hover=true]:text-secondary",
          "data-[selectable=true]:focus:border-secondary data-[selectable=true]:focus:text-secondary"
        ]
      }
    },
    {
      variant: "bordered",
      color: "success",
      class: {
        base: [
          "data-[hover=true]:border-success data-[hover=true]:text-success",
          "data-[selectable=true]:focus:border-success data-[selectable=true]:focus:text-success"
        ]
      }
    },
    {
      variant: "bordered",
      color: "warning",
      class: {
        base: [
          "data-[hover=true]:border-warning data-[hover=true]:text-warning",
          "data-[selectable=true]:focus:border-warning data-[selectable=true]:focus:text-warning"
        ]
      }
    },
    {
      variant: "bordered",
      color: "danger",
      class: {
        base: [
          "data-[hover=true]:border-danger data-[hover=true]:text-danger",
          "data-[selectable=true]:focus:border-danger data-[selectable=true]:focus:text-danger"
        ]
      }
    },
    {
      variant: "flat",
      color: "default",
      class: {
        base: [
          "data-[hover=true]:bg-default/40",
          "data-[hover=true]:text-default-foreground",
          "data-[selectable=true]:focus:bg-default/40",
          "data-[selectable=true]:focus:text-default-foreground"
        ]
      }
    },
    {
      variant: "flat",
      color: "primary",
      class: {
        base: [
          "data-[hover=true]:bg-primary/20 data-[hover=true]:text-primary",
          "data-[selectable=true]:focus:bg-primary/20 data-[selectable=true]:focus:text-primary"
        ]
      }
    },
    {
      variant: "flat",
      color: "secondary",
      class: {
        base: [
          "data-[hover=true]:bg-secondary/20 data-[hover=true]:text-secondary",
          "data-[selectable=true]:focus:bg-secondary/20 data-[selectable=true]:focus:text-secondary"
        ]
      }
    },
    {
      variant: "flat",
      color: "success",
      class: {
        base: [
          "data-[hover=true]:bg-success/20 data-[hover=true]:text-success",
          "data-[selectable=true]:focus:bg-success/20 data-[selectable=true]:focus:text-success"
        ]
      }
    },
    {
      variant: "flat",
      color: "warning",
      class: {
        base: [
          "data-[hover=true]:bg-warning/20 data-[hover=true]:text-warning",
          "data-[selectable=true]:focus:bg-warning/20 data-[selectable=true]:focus:text-warning"
        ]
      }
    },
    {
      variant: "flat",
      color: "danger",
      class: {
        base: [
          "data-[hover=true]:bg-danger/20 data-[hover=true]:text-danger",
          "data-[selectable=true]:focus:bg-danger/20 data-[selectable=true]:focus:text-danger"
        ]
      }
    },
    {
      variant: "faded",
      color: "default",
      class: {
        base: [
          "data-[hover=true]:text-default-foreground",
          "data-[selectable=true]:focus:text-default-foreground"
        ]
      }
    },
    {
      variant: "faded",
      color: "primary",
      class: {
        base: ["data-[hover=true]:text-primary", "data-[selectable=true]:focus:text-primary"]
      }
    },
    {
      variant: "faded",
      color: "secondary",
      class: {
        base: ["data-[hover=true]:text-secondary", "data-[selectable=true]:focus:text-secondary"]
      }
    },
    {
      variant: "faded",
      color: "success",
      class: {
        base: ["data-[hover=true]:text-success", "data-[selectable=true]:focus:text-success"]
      }
    },
    {
      variant: "faded",
      color: "warning",
      class: {
        base: ["data-[hover=true]:text-warning", "data-[selectable=true]:focus:text-warning"]
      }
    },
    {
      variant: "faded",
      color: "danger",
      class: {
        base: ["data-[hover=true]:text-danger", "data-[selectable=true]:focus:text-danger"]
      }
    },
    {
      variant: "light",
      color: "default",
      class: {
        base: [
          "data-[hover=true]:text-default-500",
          "data-[selectable=true]:focus:text-default-500"
        ]
      }
    },
    {
      variant: "light",
      color: "primary",
      class: {
        base: ["data-[hover=true]:text-primary", "data-[selectable=true]:focus:text-primary"]
      }
    },
    {
      variant: "light",
      color: "secondary",
      class: {
        base: ["data-[hover=true]:text-secondary", "data-[selectable=true]:focus:text-secondary"]
      }
    },
    {
      variant: "light",
      color: "success",
      class: {
        base: ["data-[hover=true]:text-success", "data-[selectable=true]:focus:text-success"]
      }
    },
    {
      variant: "light",
      color: "warning",
      class: {
        base: ["data-[hover=true]:text-warning", "data-[selectable=true]:focus:text-warning"]
      }
    },
    {
      variant: "light",
      color: "danger",
      class: {
        base: ["data-[hover=true]:text-danger", "data-[selectable=true]:focus:text-danger"]
      }
    }
  ]
});
var menuSection = tv({
  slots: {
    base: "relative mb-2",
    heading: "pl-1 text-tiny text-foreground-500",
    group: "data-[has-title=true]:pt-1",
    divider: "mt-2"
  }
});

export {
  menu,
  menuItem,
  menuSection
};
