export { absoluteFullClasses, baseStyles, collapseAdjacentVariantBorders, dataFocusVisibleClasses, focusVisibleClasses, groupDataFocusVisibleClasses, hiddenInputClasses, ringClasses, translateCenterClasses } from './classes.js';
export { SlotsToClasses } from './types.js';
export { colorVariants } from './variants.js';
export { COMMON_UNITS, twMergeConfig } from './tw-merge-config.js';
export { mergeClasses } from './merge-classes.js';
export { cn } from './cn.js';
import 'tailwind-variants';
import 'clsx';
