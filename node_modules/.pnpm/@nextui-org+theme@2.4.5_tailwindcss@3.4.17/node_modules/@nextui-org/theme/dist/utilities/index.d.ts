declare const utilities: {
    ".scrollbar-hide": {
        "-ms-overflow-style": string;
        "scrollbar-width": string;
        "&::-webkit-scrollbar": {
            display: string;
        };
    };
    ".scrollbar-default": {
        "-ms-overflow-style": string;
        "scrollbar-width": string;
        "&::-webkit-scrollbar": {
            display: string;
        };
    };
    ".transition-background": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-colors-opacity": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-width": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-height": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-size": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-left": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-transform-opacity": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-transform-background": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-transform-colors": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-transform-colors-opacity": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".leading-inherit": {
        "line-height": string;
    };
    ".bg-img-inherit": {
        "background-image": string;
    };
    ".bg-clip-inherit": {
        "background-clip": string;
    };
    ".text-fill-inherit": {
        "-webkit-text-fill-color": string;
    };
    ".tap-highlight-transparent": {
        "-webkit-tap-highlight-color": string;
    };
    ".input-search-cancel-button-none": {
        "&::-webkit-search-cancel-button": {
            "-webkit-appearance": string;
        };
    };
};

export { utilities };
