declare const DEFAULT_TRANSITION_DURATION = "250ms";
declare const _default: {
    /**
     * Transition utilities
     */
    ".transition-background": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-colors-opacity": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-width": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-height": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-size": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-left": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-transform-opacity": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-transform-background": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-transform-colors": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
    ".transition-transform-colors-opacity": {
        "transition-property": string;
        "transition-timing-function": string;
        "transition-duration": string;
    };
};

export { DEFAULT_TRANSITION_DURATION, _default as default };
